<?xml version="1.0" encoding="UTF-8"?>
<svg width="1024" height="1024" viewBox="0 0 1024 1024" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background gradient -->
  <defs>
    <radialGradient id="bgGradient" cx="50%" cy="50%" r="70%">
      <stop offset="0%" style="stop-color:#34495E;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2C3E50;stop-opacity:1" />
    </radialGradient>
    <linearGradient id="blueGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#5DADE2;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3498DB;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="redGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#EC7063;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#E74C3C;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Full background -->
  <rect width="1024" height="1024" fill="url(#bgGradient)"/>
  
  <!-- Blue square background (top-left) -->
  <rect x="0" y="0" width="400" height="400" rx="48" fill="url(#blueGradient)"/>
  
  <!-- Red square background (bottom-right) -->
  <rect x="624" y="624" width="400" height="400" rx="48" fill="url(#redGradient)"/>
  
  <!-- Main clock circle - larger to fill more space -->
  <circle cx="512" cy="512" r="320" fill="#2C3E50" stroke="#34495E" stroke-width="16"/>
  
  <!-- Outer ring - blue -->
  <circle cx="512" cy="512" r="280" fill="none" stroke="url(#blueGradient)" stroke-width="32"/>
  
  <!-- Outer ring - red -->
  <circle cx="512" cy="512" r="280" fill="none" stroke="url(#redGradient)" stroke-width="32" 
          stroke-dasharray="440 440" stroke-dashoffset="220" transform="rotate(45 512 512)"/>
  
  <!-- Inner clock face -->
  <circle cx="512" cy="512" r="200" fill="#34495E"/>
  
  <!-- Clock hands -->
  <!-- Hour hand pointing to 10 -->
  <line x1="512" y1="512" x2="452" y2="392" stroke="url(#redGradient)" stroke-width="16" stroke-linecap="round"/>
  
  <!-- Minute hand pointing to 2 -->
  <line x1="512" y1="512" x2="632" y2="412" stroke="url(#redGradient)" stroke-width="12" stroke-linecap="round"/>
  
  <!-- Center dot -->
  <circle cx="512" cy="512" r="16" fill="url(#redGradient)"/>
  
  <!-- Small accent circle (top right) -->
  <circle cx="768" cy="256" r="48" fill="url(#redGradient)"/>
  
  <!-- Clock markers -->
  <circle cx="512" cy="352" r="8" fill="url(#blueGradient)"/> <!-- 12 o'clock -->
  <circle cx="672" cy="512" r="8" fill="url(#blueGradient)"/> <!-- 3 o'clock -->
  <circle cx="512" cy="672" r="8" fill="url(#blueGradient)"/> <!-- 6 o'clock -->
  <circle cx="352" cy="512" r="8" fill="url(#blueGradient)"/> <!-- 9 o'clock -->
  
  <!-- Additional decorative elements to fill corners -->
  <!-- Top-left corner accent -->
  <path d="M 0 200 Q 0 0 200 0 L 0 0 Z" fill="url(#blueGradient)" opacity="0.3"/>
  
  <!-- Bottom-right corner accent -->
  <path d="M 824 1024 Q 1024 1024 1024 824 L 1024 1024 Z" fill="url(#redGradient)" opacity="0.3"/>
  
  <!-- Top-right corner subtle accent -->
  <circle cx="824" cy="200" r="24" fill="url(#blueGradient)" opacity="0.6"/>
  
  <!-- Bottom-left corner subtle accent -->
  <circle cx="200" cy="824" r="24" fill="url(#redGradient)" opacity="0.6"/>
</svg>
