// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'assignment.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Assignment _$AssignmentFromJson(Map<String, dynamic> json) => Assignment(
      id: (json['id'] as num).toInt(),
      userId: (json['user_id'] as num).toInt(),
      siteId: (json['site_id'] as num).toInt(),
      createdAt: json['created_at'] as String?,
      updatedAt: json['updated_at'] as String?,
      user: json['user'] == null
          ? null
          : User.fromJson(json['user'] as Map<String, dynamic>),
      site: json['site'] == null
          ? null
          : Site.fromJson(json['site'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$AssignmentToJson(Assignment instance) =>
    <String, dynamic>{
      'id': instance.id,
      'user_id': instance.userId,
      'site_id': instance.siteId,
      'created_at': instance.createdAt,
      'updated_at': instance.updatedAt,
      'user': instance.user,
      'site': instance.site,
    };

UserAssignment _$UserAssignmentFromJson(Map<String, dynamic> json) =>
    UserAssignment(
      user: User.fromJson(json['user'] as Map<String, dynamic>),
      assignedSites: (json['assigned_sites'] as List<dynamic>)
          .map((e) => Site.fromJson(e as Map<String, dynamic>))
          .toList(),
      activePointages: (json['active_pointages'] as num).toInt(),
      totalPointages: (json['total_pointages'] as num).toInt(),
    );

Map<String, dynamic> _$UserAssignmentToJson(UserAssignment instance) =>
    <String, dynamic>{
      'user': instance.user,
      'assigned_sites': instance.assignedSites,
      'active_pointages': instance.activePointages,
      'total_pointages': instance.totalPointages,
    };

SiteAssignment _$SiteAssignmentFromJson(Map<String, dynamic> json) =>
    SiteAssignment(
      site: Site.fromJson(json['site'] as Map<String, dynamic>),
      assignedUsers: (json['assigned_users'] as List<dynamic>)
          .map((e) => User.fromJson(e as Map<String, dynamic>))
          .toList(),
      activePointages: (json['active_pointages'] as num).toInt(),
      totalPointages: (json['total_pointages'] as num).toInt(),
    );

Map<String, dynamic> _$SiteAssignmentToJson(SiteAssignment instance) =>
    <String, dynamic>{
      'site': instance.site,
      'assigned_users': instance.assignedUsers,
      'active_pointages': instance.activePointages,
      'total_pointages': instance.totalPointages,
    };

AssignmentStats _$AssignmentStatsFromJson(Map<String, dynamic> json) =>
    AssignmentStats(
      totalAssignments: (json['total_assignments'] as num).toInt(),
      activeAssignments: (json['active_assignments'] as num).toInt(),
      usersWithAssignments: (json['users_with_assignments'] as num).toInt(),
      sitesWithAssignments: (json['sites_with_assignments'] as num).toInt(),
      unassignedUsers: (json['unassigned_users'] as num).toInt(),
      unassignedSites: (json['unassigned_sites'] as num).toInt(),
    );

Map<String, dynamic> _$AssignmentStatsToJson(AssignmentStats instance) =>
    <String, dynamic>{
      'total_assignments': instance.totalAssignments,
      'active_assignments': instance.activeAssignments,
      'users_with_assignments': instance.usersWithAssignments,
      'sites_with_assignments': instance.sitesWithAssignments,
      'unassigned_users': instance.unassignedUsers,
      'unassigned_sites': instance.unassignedSites,
    };
