// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'site.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Site _$SiteFromJson(Map<String, dynamic> json) => Site(
      id: (json['id'] as num).toInt(),
      name: json['name'] as String,
      latitude: _doubleFromString(json['latitude']),
      longitude: _doubleFromString(json['longitude']),
      rayon: json['rayon'] == null ? 50.0 : _doubleFromString(json['rayon']),
      users: (json['users'] as List<dynamic>?)
          ?.map((e) => User.fromJson(e as Map<String, dynamic>))
          .toList(),
      pointagesCount: (json['pointages_count'] as num?)?.toInt(),
      createdAt: json['created_at'] as String?,
      updatedAt: json['updated_at'] as String?,
      assignedEmployeeIds: (json['assigned_employee_ids'] as List<dynamic>?)
          ?.map((e) => (e as num).toInt())
          .toList(),
    );

Map<String, dynamic> _$SiteToJson(Site instance) => <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'latitude': instance.latitude,
      'longitude': instance.longitude,
      'rayon': instance.rayon,
      'users': instance.users,
      'pointages_count': instance.pointagesCount,
      'created_at': instance.createdAt,
      'updated_at': instance.updatedAt,
      'assigned_employee_ids': instance.assignedEmployeeIds,
    };

SiteCreateRequest _$SiteCreateRequestFromJson(Map<String, dynamic> json) =>
    SiteCreateRequest(
      name: json['name'] as String,
      latitude: (json['latitude'] as num).toDouble(),
      longitude: (json['longitude'] as num).toDouble(),
    );

Map<String, dynamic> _$SiteCreateRequestToJson(SiteCreateRequest instance) =>
    <String, dynamic>{
      'name': instance.name,
      'latitude': instance.latitude,
      'longitude': instance.longitude,
    };

SiteUpdateRequest _$SiteUpdateRequestFromJson(Map<String, dynamic> json) =>
    SiteUpdateRequest(
      name: json['name'] as String?,
      latitude: (json['latitude'] as num?)?.toDouble(),
      longitude: (json['longitude'] as num?)?.toDouble(),
    );

Map<String, dynamic> _$SiteUpdateRequestToJson(SiteUpdateRequest instance) =>
    <String, dynamic>{
      'name': instance.name,
      'latitude': instance.latitude,
      'longitude': instance.longitude,
    };

SiteAssignmentRequest _$SiteAssignmentRequestFromJson(
        Map<String, dynamic> json) =>
    SiteAssignmentRequest(
      userId: (json['user_id'] as num).toInt(),
      siteId: (json['site_id'] as num).toInt(),
    );

Map<String, dynamic> _$SiteAssignmentRequestToJson(
        SiteAssignmentRequest instance) =>
    <String, dynamic>{
      'user_id': instance.userId,
      'site_id': instance.siteId,
    };

LocationCheckRequest _$LocationCheckRequestFromJson(
        Map<String, dynamic> json) =>
    LocationCheckRequest(
      siteId: (json['site_id'] as num?)?.toInt(),
      latitude: (json['latitude'] as num).toDouble(),
      longitude: (json['longitude'] as num).toDouble(),
    );

Map<String, dynamic> _$LocationCheckRequestToJson(
        LocationCheckRequest instance) =>
    <String, dynamic>{
      if (instance.siteId case final value?) 'site_id': value,
      'latitude': instance.latitude,
      'longitude': instance.longitude,
    };

LocationCheckResponse _$LocationCheckResponseFromJson(
        Map<String, dynamic> json) =>
    LocationCheckResponse(
      inRange: json['in_range'] as bool,
      distance: (json['distance'] as num).toDouble(),
      maxDistance: (json['max_distance'] as num).toDouble(),
      site: Site.fromJson(json['site'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$LocationCheckResponseToJson(
        LocationCheckResponse instance) =>
    <String, dynamic>{
      'in_range': instance.inRange,
      'distance': instance.distance,
      'max_distance': instance.maxDistance,
      'site': instance.site,
    };

SiteWithAssignments _$SiteWithAssignmentsFromJson(Map<String, dynamic> json) =>
    SiteWithAssignments(
      site: Site.fromJson(json['site'] as Map<String, dynamic>),
      assignedUsers: (json['assigned_users'] as List<dynamic>)
          .map((e) => (e as num).toInt())
          .toList(),
      activePointages: (json['active_pointages'] as num).toInt(),
    );

Map<String, dynamic> _$SiteWithAssignmentsToJson(
        SiteWithAssignments instance) =>
    <String, dynamic>{
      'site': instance.site,
      'assigned_users': instance.assignedUsers,
      'active_pointages': instance.activePointages,
    };
