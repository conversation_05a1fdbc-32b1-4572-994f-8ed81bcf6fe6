import 'package:flutter/material.dart';
import '../../constants/app_colors.dart';
import 'full_coverage_logo.dart';

enum LogoType {
  full,
  icon,
  compact,
  image, // New type for PNG logo
  fullCoverage, // New type for full coverage logo
}

class AppLogo extends StatelessWidget {
  final LogoType type;
  final double? width;
  final double? height;
  final Color? color;

  const AppLogo({
    super.key,
    this.type = LogoType.icon,
    this.width,
    this.height,
    this.color,
  });

  @override
  Widget build(BuildContext context) {
    // Use PNG logo if available, otherwise fallback to custom painted logo
    if (type == LogoType.image) {
      return _buildImageLogo();
    }
    if (type == LogoType.fullCoverage) {
      return _buildFullCoverageLogo();
    }
    return _buildCustomLogo();
  }

  Widget _buildImageLogo() {
    return Image.asset(
      'assets/logo/logo.png',
      width: width,
      height: height,
      fit: BoxFit.contain,
      errorBuilder: (context, error, stackTrace) {
        // Fallback to icon logo if image fails to load
        return _buildIconLogo();
      },
    );
  }

  Widget _buildFullCoverageLogo() {
    return FullCoverageLogo(size: width ?? height ?? 120, showBackground: true);
  }

  Widget _buildCustomLogo() {
    switch (type) {
      case LogoType.full:
        return _buildFullLogo();
      case LogoType.compact:
        return _buildCompactLogo();
      case LogoType.icon:
        return _buildIconLogo();
      case LogoType.image:
        return _buildImageLogo();
      case LogoType.fullCoverage:
        return _buildFullCoverageLogo();
    }
  }

  Widget _buildFullLogo() {
    return SizedBox(
      width: width ?? 200,
      height: height ?? 80,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildClockIcon(size: height ?? 80),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Timetrackr',
                  style: TextStyle(
                    fontSize: (height ?? 80) * 0.3,
                    fontWeight: FontWeight.bold,
                    color: color ?? AppColors.background,
                  ),
                ),
                Text(
                  'نظام إدارة الحضور',
                  style: TextStyle(
                    fontSize: (height ?? 80) * 0.15,
                    color:
                        color?.withValues(alpha: 0.7) ??
                        AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCompactLogo() {
    return SizedBox(
      width: width ?? 120,
      height: height ?? 40,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildClockIcon(size: height ?? 40),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Timetrackr',
                  style: TextStyle(
                    fontSize: (height ?? 40) * 0.35,
                    fontWeight: FontWeight.bold,
                    color: color ?? AppColors.background,
                  ),
                ),
                Text(
                  'نظام الحضور',
                  style: TextStyle(
                    fontSize: (height ?? 40) * 0.2,
                    color:
                        color?.withValues(alpha: 0.7) ??
                        AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildIconLogo() {
    return _buildClockIcon(size: width ?? height ?? 80);
  }

  Widget _buildClockIcon({required double size}) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: color ?? AppColors.background,
        shape: BoxShape.circle,
        border: Border.all(
          color: color?.withValues(alpha: 0.8) ?? AppColors.textPrimary,
          width: size * 0.025,
        ),
        boxShadow: [
          BoxShadow(
            color: (color ?? AppColors.background).withValues(alpha: 0.3),
            blurRadius: size * 0.1,
            offset: Offset(0, size * 0.05),
          ),
        ],
      ),
      child: Stack(
        children: [
          // Clock face
          Center(
            child: Container(
              width: size * 0.65,
              height: size * 0.65,
              decoration: BoxDecoration(
                color: AppColors.buttonBackground,
                shape: BoxShape.circle,
                border: Border.all(
                  color: color ?? AppColors.background,
                  width: size * 0.02,
                ),
              ),
            ),
          ),
          // Clock hands
          Center(
            child: CustomPaint(
              size: Size(size, size),
              painter: ClockHandsPainter(
                color: color ?? AppColors.background,
                size: size,
              ),
            ),
          ),
          // Check mark
          Positioned(
            top: size * 0.15,
            right: size * 0.15,
            child: Icon(
              Icons.check,
              color: AppColors.textPrimary,
              size: size * 0.2,
            ),
          ),
        ],
      ),
    );
  }
}

class ClockHandsPainter extends CustomPainter {
  final Color color;
  final double size;

  ClockHandsPainter({required this.color, required this.size});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = this.size * 0.02
      ..strokeCap = StrokeCap.round;

    final center = Offset(size.width / 2, size.height / 2);
    final radius = this.size * 0.32;

    // Hour hand (pointing to 10)
    final hourHandEnd = Offset(
      center.dx + radius * 0.6 * 0.5, // cos(60°) ≈ 0.5
      center.dy - radius * 0.6 * 0.866, // sin(60°) ≈ 0.866
    );
    canvas.drawLine(
      center,
      hourHandEnd,
      paint..strokeWidth = this.size * 0.025,
    );

    // Minute hand (pointing to 2)
    final minuteHandEnd = Offset(
      center.dx + radius * 0.8 * 0.866, // cos(30°) ≈ 0.866
      center.dy - radius * 0.8 * 0.5, // sin(30°) ≈ 0.5
    );
    canvas.drawLine(
      center,
      minuteHandEnd,
      paint..strokeWidth = this.size * 0.02,
    );

    // Center dot
    canvas.drawCircle(center, this.size * 0.02, paint..strokeWidth = 0);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
