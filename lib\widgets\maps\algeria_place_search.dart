import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'dart:async';
import '../../constants/app_colors.dart';

class AlgeriaPlaceSearch extends StatefulWidget {
  final Function(LatLng location, String placeName, String address) onPlaceSelected;
  final String? initialQuery;

  const AlgeriaPlaceSearch({
    super.key,
    required this.onPlaceSelected,
    this.initialQuery,
  });

  @override
  State<AlgeriaPlaceSearch> createState() => _AlgeriaPlaceSearchState();
}

class _AlgeriaPlaceSearchState extends State<AlgeriaPlaceSearch> {
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _focusNode = FocusNode();
  List<PlaceSearchResult> _searchResults = [];
  bool _isSearching = false;
  bool _showResults = false;

  // Clé API Google Maps (remplacez par votre vraie clé)
  static const String _googleApiKey = 'YOUR_GOOGLE_MAPS_API_KEY';

  // Limites géographiques de l'Algérie
  static const double _algeriaMinLat = 18.9681;
  static const double _algeriaMaxLat = 37.2962;
  static const double _algeriaMinLng = -8.6676;
  static const double _algeriaMaxLng = 11.9795;

  @override
  void initState() {
    super.initState();
    if (widget.initialQuery != null) {
      _searchController.text = widget.initialQuery!;
    }
  }

  @override
  void dispose() {
    _searchController.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSearchField(),
        if (_showResults && _searchResults.isNotEmpty) ...[
          const SizedBox(height: 8),
          _buildSearchResults(),
        ],
      ],
    );
  }

  Widget _buildSearchField() {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.buttonBackground,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.textSecondary),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TextField(
        controller: _searchController,
        focusNode: _focusNode,
        decoration: InputDecoration(
          hintText: 'ابحث عن أي مكان في الجزائر (مثال: جامعة الجزائر، مطار هواري بومدين...)',
          hintStyle: TextStyle(
            color: AppColors.textSecondary,
            fontSize: 14,
          ),
          prefixIcon: Icon(
            Icons.search,
            color: AppColors.background,
          ),
          suffixIcon: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (_isSearching)
                const Padding(
                  padding: EdgeInsets.all(12),
                  child: SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  ),
                ),
              if (_searchController.text.isNotEmpty)
                IconButton(
                  icon: const Icon(Icons.clear),
                  onPressed: _clearSearch,
                  color: AppColors.textSecondary,
                ),
            ],
          ),
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 16,
          ),
        ),
        onChanged: _onSearchChanged,
        onSubmitted: _performSearch,
        textInputAction: TextInputAction.search,
      ),
    );
  }

  Widget _buildSearchResults() {
    return Container(
      constraints: const BoxConstraints(maxHeight: 300),
      decoration: BoxDecoration(
        color: AppColors.buttonBackground,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.textSecondary),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ListView.separated(
        shrinkWrap: true,
        itemCount: _searchResults.length,
        separatorBuilder: (context, index) => Divider(
          height: 1,
          color: AppColors.textSecondary,
        ),
        itemBuilder: (context, index) {
          final result = _searchResults[index];
          return ListTile(
            leading: CircleAvatar(
              backgroundColor: AppColors.background.withValues(alpha: 0.1),
              child: Icon(
                _getPlaceIcon(result.types),
                color: AppColors.background,
                size: 20,
              ),
            ),
            title: Text(
              result.name,
              style: const TextStyle(
                fontWeight: FontWeight.w600,
                fontSize: 14,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
            subtitle: Text(
              result.formattedAddress,
              style: TextStyle(
                color: AppColors.textSecondary,
                fontSize: 12,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            trailing: Icon(
              Icons.arrow_forward_ios,
              size: 16,
              color: AppColors.textSecondary,
            ),
            onTap: () => _selectPlace(result),
          );
        },
      ),
    );
  }

  IconData _getPlaceIcon(List<String> types) {
    if (types.contains('university') || types.contains('school')) {
      return Icons.school;
    } else if (types.contains('hospital') || types.contains('pharmacy')) {
      return Icons.local_hospital;
    } else if (types.contains('airport')) {
      return Icons.flight;
    } else if (types.contains('bank')) {
      return Icons.account_balance;
    } else if (types.contains('restaurant') || types.contains('food')) {
      return Icons.restaurant;
    } else if (types.contains('gas_station')) {
      return Icons.local_gas_station;
    } else if (types.contains('shopping_mall') || types.contains('store')) {
      return Icons.shopping_cart;
    } else if (types.contains('mosque')) {
      return Icons.mosque;
    } else if (types.contains('government') || types.contains('city_hall')) {
      return Icons.account_balance;
    } else if (types.contains('park')) {
      return Icons.park;
    } else {
      return Icons.place;
    }
  }

  void _onSearchChanged(String query) {
    if (query.length >= 3) {
      _debounceSearch(query);
    } else {
      setState(() {
        _searchResults.clear();
        _showResults = false;
      });
    }
  }

  Timer? _debounceTimer;
  void _debounceSearch(String query) {
    _debounceTimer?.cancel();
    _debounceTimer = Timer(const Duration(milliseconds: 500), () {
      _performSearch(query);
    });
  }

  Future<void> _performSearch(String query) async {
    if (query.trim().isEmpty) return;

    setState(() {
      _isSearching = true;
      _showResults = true;
    });

    try {
      // Recherche avec Google Places API
      final results = await _searchWithGooglePlaces(query);
      
      // Filtrer les résultats pour l'Algérie uniquement
      final algerianResults = results.where((result) {
        return _isInAlgeria(result.location) && 
               result.formattedAddress.toLowerCase().contains('algeria') ||
               result.formattedAddress.toLowerCase().contains('algérie') ||
               result.formattedAddress.toLowerCase().contains('الجزائر');
      }).toList();

      setState(() {
        _searchResults = algerianResults;
        _isSearching = false;
      });
    } catch (e) {
      debugPrint('Erreur de recherche: $e');
      setState(() {
        _searchResults.clear();
        _isSearching = false;
      });
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في البحث: ${e.toString()}'),
            backgroundColor: AppColors.textPrimary,
          ),
        );
      }
    }
  }

  Future<List<PlaceSearchResult>> _searchWithGooglePlaces(String query) async {
    // Ajouter "Algeria" à la recherche pour limiter aux résultats algériens
    final searchQuery = '$query, Algeria';
    
    final url = Uri.parse(
      'https://maps.googleapis.com/maps/api/place/textsearch/json'
      '?query=${Uri.encodeComponent(searchQuery)}'
      '&key=$_googleApiKey'
      '&language=ar'
      '&region=dz'
    );

    final response = await http.get(url);
    
    if (response.statusCode == 200) {
      final data = json.decode(response.body);
      
      if (data['status'] == 'OK') {
        final results = <PlaceSearchResult>[];
        
        for (final place in data['results']) {
          final location = LatLng(
            place['geometry']['location']['lat'].toDouble(),
            place['geometry']['location']['lng'].toDouble(),
          );
          
          results.add(PlaceSearchResult(
            placeId: place['place_id'],
            name: place['name'],
            formattedAddress: place['formatted_address'],
            location: location,
            types: List<String>.from(place['types'] ?? []),
          ));
        }
        
        return results;
      } else {
        throw Exception('Google Places API Error: ${data['status']}');
      }
    } else {
      throw Exception('HTTP Error: ${response.statusCode}');
    }
  }

  bool _isInAlgeria(LatLng location) {
    return location.latitude >= _algeriaMinLat &&
           location.latitude <= _algeriaMaxLat &&
           location.longitude >= _algeriaMinLng &&
           location.longitude <= _algeriaMaxLng;
  }

  void _selectPlace(PlaceSearchResult result) {
    _searchController.text = result.name;
    setState(() {
      _showResults = false;
    });
    _focusNode.unfocus();
    
    widget.onPlaceSelected(
      result.location,
      result.name,
      result.formattedAddress,
    );
  }

  void _clearSearch() {
    _searchController.clear();
    setState(() {
      _searchResults.clear();
      _showResults = false;
    });
  }
}

class PlaceSearchResult {
  final String placeId;
  final String name;
  final String formattedAddress;
  final LatLng location;
  final List<String> types;

  PlaceSearchResult({
    required this.placeId,
    required this.name,
    required this.formattedAddress,
    required this.location,
    required this.types,
  });
}
