// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'pointage.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Pointage _$PointageFromJson(Map<String, dynamic> json) => Pointage(
      id: (json['id'] as num).toInt(),
      userId: (json['user_id'] as num).toInt(),
      siteId: (json['site_id'] as num).toInt(),
      debutPointage: DateTime.parse(json['debut_pointage'] as String),
      finPointage: json['fin_pointage'] == null
          ? null
          : DateTime.parse(json['fin_pointage'] as String),
      duree: json['duree'] as String?,
      debutLatitude: json['debut_latitude'] as String?,
      debutLongitude: json['debut_longitude'] as String?,
      finLatitude: json['fin_latitude'] as String?,
      finLongitude: json['fin_longitude'] as String?,
      createdAt: json['created_at'] as String?,
      updatedAt: json['updated_at'] as String?,
      user: json['user'] == null
          ? null
          : User.fromJson(json['user'] as Map<String, dynamic>),
      site: json['site'] == null
          ? null
          : Site.fromJson(json['site'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$PointageToJson(Pointage instance) => <String, dynamic>{
      'id': instance.id,
      'user_id': instance.userId,
      'site_id': instance.siteId,
      'debut_pointage': instance.debutPointage.toIso8601String(),
      'fin_pointage': instance.finPointage?.toIso8601String(),
      'duree': instance.duree,
      'debut_latitude': instance.debutLatitude,
      'debut_longitude': instance.debutLongitude,
      'fin_latitude': instance.finLatitude,
      'fin_longitude': instance.finLongitude,
      'created_at': instance.createdAt,
      'updated_at': instance.updatedAt,
      'user': instance.user,
      'site': instance.site,
    };

PointageCreateRequest _$PointageCreateRequestFromJson(
        Map<String, dynamic> json) =>
    PointageCreateRequest(
      latitude: (json['latitude'] as num).toDouble(),
      longitude: (json['longitude'] as num).toDouble(),
      timestamp: (json['timestamp'] as num).toInt(),
      accuracy: (json['accuracy'] as num?)?.toDouble(),
      exactTime: json['exactTime'] as String?,
    );

Map<String, dynamic> _$PointageCreateRequestToJson(
        PointageCreateRequest instance) =>
    <String, dynamic>{
      'latitude': instance.latitude,
      'longitude': instance.longitude,
      'accuracy': instance.accuracy,
      'timestamp': instance.timestamp,
      'exactTime': instance.exactTime,
    };

PointageResponse _$PointageResponseFromJson(Map<String, dynamic> json) =>
    PointageResponse(
      pointage: Pointage.fromJson(json['pointage'] as Map<String, dynamic>),
      type: json['type'] as String,
      message: json['message'] as String,
      messageAr: json['message_ar'] as String,
    );

Map<String, dynamic> _$PointageResponseToJson(PointageResponse instance) =>
    <String, dynamic>{
      'pointage': instance.pointage,
      'type': instance.type,
      'message': instance.message,
      'message_ar': instance.messageAr,
    };

PointageStats _$PointageStatsFromJson(Map<String, dynamic> json) =>
    PointageStats(
      totalPointages: (json['total_pointages'] as num).toInt(),
      activePointages: (json['active_pointages'] as num).toInt(),
      completedPointages: (json['completed_pointages'] as num).toInt(),
      totalHours: (json['total_hours'] as num).toDouble(),
      averageHours: (json['average_hours'] as num).toDouble(),
      thisMonthHours: (json['this_month_hours'] as num).toDouble(),
      thisWeekHours: (json['this_week_hours'] as num).toDouble(),
    );

Map<String, dynamic> _$PointageStatsToJson(PointageStats instance) =>
    <String, dynamic>{
      'total_pointages': instance.totalPointages,
      'active_pointages': instance.activePointages,
      'completed_pointages': instance.completedPointages,
      'total_hours': instance.totalHours,
      'average_hours': instance.averageHours,
      'this_month_hours': instance.thisMonthHours,
      'this_week_hours': instance.thisWeekHours,
    };

CheckInRequest _$CheckInRequestFromJson(Map<String, dynamic> json) =>
    CheckInRequest(
      siteId: (json['site_id'] as num).toInt(),
      latitude: (json['latitude'] as num).toDouble(),
      longitude: (json['longitude'] as num).toDouble(),
    );

Map<String, dynamic> _$CheckInRequestToJson(CheckInRequest instance) =>
    <String, dynamic>{
      'site_id': instance.siteId,
      'latitude': instance.latitude,
      'longitude': instance.longitude,
    };

CheckOutRequest _$CheckOutRequestFromJson(Map<String, dynamic> json) =>
    CheckOutRequest(
      latitude: (json['latitude'] as num).toDouble(),
      longitude: (json['longitude'] as num).toDouble(),
    );

Map<String, dynamic> _$CheckOutRequestToJson(CheckOutRequest instance) =>
    <String, dynamic>{
      'latitude': instance.latitude,
      'longitude': instance.longitude,
    };
