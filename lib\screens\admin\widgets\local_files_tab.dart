import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../../../constants/app_colors.dart';
import '../../../constants/app_constants.dart';
import '../../../providers/providers.dart';
import '../../../models/models.dart';

/// Onglet pour gérer les fichiers Excel sauvegardés localement
class LocalFilesTab extends StatefulWidget {
  const LocalFilesTab({super.key});

  @override
  State<LocalFilesTab> createState() => _LocalFilesTabState();
}

class _LocalFilesTabState extends State<LocalFilesTab> {
  List<FileInfo> _savedFiles = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadSavedFiles();
  }

  Future<void> _loadSavedFiles() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final reportsProvider = context.read<ReportsProvider>();
      final files = await reportsProvider.getSavedReports();

      if (mounted) {
        setState(() {
          _savedFiles = files;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل في تحميل الملفات المحفوظة: $e', style: TextStyle(color: AppColors.background)),
            backgroundColor: AppColors.buttonText,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: RefreshIndicator(
        onRefresh: _loadSavedFiles,
        child: _isLoading
            ? const Center(child: CircularProgressIndicator(color: AppColors.buttonText))
            : _savedFiles.isEmpty
            ? _buildEmptyState()
            : _buildFilesList(),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _loadSavedFiles,
        backgroundColor: AppColors.buttonBackground,
        child: const Icon(Icons.refresh, color: AppColors.buttonText),
        tooltip: 'تحديث قائمة الملفات',
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(32),
              decoration: BoxDecoration(
                color: AppColors.buttonBackground,
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.folder_open,
                size: 64,
                color: AppColors.buttonText.withOpacity(0.7),
              ),
            ),
            const SizedBox(height: 24),
            Text(
              'لا توجد ملفات Excel محفوظة',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                color: AppColors.buttonText,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            Text(
              'قم بإنشاء تقارير Excel من الصفحة الأولى لتظهر هنا',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(color: AppColors.textPrimary),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: AppColors.buttonBackground,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: AppColors.buttonText.withOpacity(0.3),
                ),
              ),
              child: Column(
                children: [
                  Icon(Icons.info_outline, color: AppColors.buttonText, size: 24),
                  const SizedBox(height: 8),
                  Text(
                    'ملاحظة',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: AppColors.buttonText,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'ستجد ملفات Excel المحفوظة في:\nالتحميلات > ClockIn_Reports',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      color: AppColors.buttonText.withOpacity(0.8),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: _loadSavedFiles,
              icon: const Icon(Icons.refresh, color: AppColors.textPrimary),
              label: const Text('تحديث', style: TextStyle(color: AppColors.textPrimary)),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.buttonBackground,
                foregroundColor: AppColors.buttonText,
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFilesList() {
    return ListView.builder(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      itemCount: _savedFiles.length + 1, // +1 for header
      itemBuilder: (context, index) {
        if (index == 0) {
          return _buildHeader();
        }

        final file = _savedFiles[index - 1];
        return _buildFileCard(file);
      },
    );
  }

  Widget _buildHeader() {
    return Container(
      margin: const EdgeInsets.only(bottom: AppConstants.defaultPadding),
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppColors.buttonText,
            AppColors.buttonText.withOpacity(0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: AppColors.background.withOpacity(0.2),
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Icon(Icons.folder_open, color: AppColors.textPrimary, size: 32),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'الملفات المحفوظة',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    color: AppColors.textPrimary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '${_savedFiles.length} ملف Excel محفوظ',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppColors.textPrimary.withOpacity(0.9),
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: AppColors.background.withOpacity(0.2),
              borderRadius: BorderRadius.circular(16),
            ),
            child: Text(
              '${_savedFiles.length}',
              style: const TextStyle(
                color: AppColors.textPrimary,
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFileCard(FileInfo file) {
    return Card(
      elevation: 2,
      margin: const EdgeInsets.only(bottom: AppConstants.defaultPadding),
      color: AppColors.background,
      child: ListTile(
        contentPadding: const EdgeInsets.all(16),
        leading: Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: AppColors.buttonBackground,
            borderRadius: BorderRadius.circular(8),
          ),
          child: const Icon(
            Icons.table_chart,
            color: AppColors.buttonText,
            size: 24,
          ),
        ),
        title: Text(
          file.name,
          style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16, color: AppColors.textPrimary),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 8),
            Row(
              children: [
                Icon(Icons.storage, size: 16, color: AppColors.textPrimary),
                const SizedBox(width: 4),
                Text('الحجم: ${_formatFileSize(file.size)}', style: TextStyle(color: AppColors.textPrimary)),
              ],
            ),
            const SizedBox(height: 4),
            Row(
              children: [
                Icon(Icons.access_time, size: 16, color: AppColors.textPrimary),
                const SizedBox(width: 4),
                Text('تاريخ الحفظ: ${_formatDateTime(file.modifiedDate)}', style: TextStyle(color: AppColors.textPrimary)),
              ],
            ),
          ],
        ),
        trailing: PopupMenuButton<String>(
          onSelected: (value) => _handleMenuAction(value, file),
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'open',
              child: ListTile(
                leading: Icon(Icons.open_in_new, color: AppColors.buttonText),
                title: Text('فتح بـ Excel', style: TextStyle(color: AppColors.textPrimary)),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            const PopupMenuItem(
              value: 'share_whatsapp',
              child: ListTile(
                leading: Icon(Icons.chat, color: Colors.green),
                title: Text('مشاركة عبر واتساب', style: TextStyle(color: AppColors.textPrimary)),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            const PopupMenuItem(
              value: 'share_email',
              child: ListTile(
                leading: Icon(Icons.email, color: Colors.blue),
                title: Text('مشاركة عبر الإيميل', style: TextStyle(color: AppColors.textPrimary)),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            const PopupMenuItem(
              value: 'share',
              child: ListTile(
                leading: Icon(Icons.share, color: AppColors.buttonText),
                title: Text('مشاركة', style: TextStyle(color: AppColors.textPrimary)),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            const PopupMenuItem(
              value: 'delete',
              child: ListTile(
                leading: Icon(Icons.delete, color: AppColors.buttonText),
                title: Text('حذف', style: TextStyle(color: AppColors.buttonText)),
                contentPadding: EdgeInsets.zero,
              ),
            ),
          ],
        ),
        onTap: () => _openFile(file),
      ),
    );
  }

  Future<void> _handleMenuAction(String action, FileInfo file) async {
    switch (action) {
      case 'open':
        await _openFile(file);
        break;
      case 'share_whatsapp':
        await _shareViaWhatsApp(file);
        break;
      case 'share_email':
        await _shareViaEmail(file);
        break;
      case 'share':
        await _shareFile(file);
        break;
      case 'delete':
        await _deleteFile(file);
        break;
    }
  }

  Future<void> _openFile(FileInfo file) async {
    try {
      final reportsProvider = context.read<ReportsProvider>();
      final opened = await reportsProvider.openExcelFile(file.path);

      if (!opened && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'لا يمكن فتح الملف. تأكد من وجود تطبيق Excel أو Google Sheets',
              style: TextStyle(color: AppColors.background),
            ),
            backgroundColor: AppColors.buttonText,
            action: SnackBarAction(
              label: 'مشاركة',
              textColor: AppColors.background,
              onPressed: () => _shareFile(file),
            ),
          ),
        );
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم فتح الملف بنجاح', style: TextStyle(color: AppColors.background)),
            backgroundColor: AppColors.buttonText,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل في فتح الملف: $e', style: TextStyle(color: AppColors.background)),
            backgroundColor: AppColors.buttonText,
          ),
        );
      }
    }
  }

  Future<void> _shareViaWhatsApp(FileInfo file) async {
    try {
      final reportsProvider = context.read<ReportsProvider>();
      final shared = await reportsProvider.shareViaWhatsApp(
        file.path,
        file.name,
      );

      if (shared && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم فتح واتساب لمشاركة الملف', style: TextStyle(color: AppColors.background)),
            backgroundColor: AppColors.buttonText,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل في مشاركة الملف عبر واتساب: $e', style: TextStyle(color: AppColors.background)),
            backgroundColor: AppColors.buttonText,
          ),
        );
      }
    }
  }

  Future<void> _shareViaEmail(FileInfo file) async {
    try {
      final reportsProvider = context.read<ReportsProvider>();
      final shared = await reportsProvider.shareViaEmail(file.path, file.name);

      if (shared && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم فتح تطبيق الإيميل لمشاركة الملف', style: TextStyle(color: AppColors.background)),
            backgroundColor: AppColors.buttonText,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل في مشاركة الملف عبر الإيميل: $e', style: TextStyle(color: AppColors.background)),
            backgroundColor: AppColors.buttonText,
          ),
        );
      }
    }
  }

  Future<void> _shareFile(FileInfo file) async {
    try {
      final reportsProvider = context.read<ReportsProvider>();
      final shared = await reportsProvider.shareExcelFile(file.path, file.name);

      if (shared && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم فتح قائمة التطبيقات لمشاركة الملف', style: TextStyle(color: AppColors.background)),
            backgroundColor: AppColors.buttonText,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل في مشاركة الملف: $e', style: TextStyle(color: AppColors.background)),
            backgroundColor: AppColors.buttonText,
          ),
        );
      }
    }
  }

  Future<void> _deleteFile(FileInfo file) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppColors.background,
        title: Row(
          children: [
            Icon(Icons.warning, color: AppColors.buttonText),
            SizedBox(width: 8),
            Text('تأكيد الحذف', style: TextStyle(color: AppColors.textPrimary)),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('هل تريد حذف الملف التالي؟', style: TextStyle(color: AppColors.textPrimary)),
            SizedBox(height: 8),
            Container(
              padding: EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppColors.background,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: AppColors.buttonText.withOpacity(0.3)),
              ),
              child: Row(
                children: [
                  Icon(Icons.table_chart, color: AppColors.buttonText),
                  SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      file.name,
                      style: TextStyle(fontWeight: FontWeight.bold, color: AppColors.textPrimary),
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(height: 8),
            Text(
              'لن يمكن استرداد الملف بعد الحذف.',
              style: TextStyle(color: AppColors.buttonText, fontSize: 12),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء', style: TextStyle(color: AppColors.buttonText)),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.buttonText,
              foregroundColor: AppColors.textPrimary,
            ),
            child: const Text('حذف', style: TextStyle(color: AppColors.textPrimary)),
          ),
        ],
      ),
    );

    if (confirmed == true && mounted) {
      try {
        final reportsProvider = context.read<ReportsProvider>();
        final deleted = await reportsProvider.deleteReport(file.path);

        if (deleted && mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم حذف الملف بنجاح', style: TextStyle(color: AppColors.background)),
              backgroundColor: AppColors.buttonText,
            ),
          );

          // إعادة تحميل قائمة الملفات
          await _loadSavedFiles();
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('فشل في حذف الملف: $e', style: TextStyle(color: AppColors.background)),
              backgroundColor: AppColors.buttonText,
            ),
          );
        }
      }
    }
  }

  String _formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024)
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  String _formatDateTime(DateTime dateTime) {
    return DateFormat('dd/MM/yyyy HH:mm').format(dateTime);
  }
}