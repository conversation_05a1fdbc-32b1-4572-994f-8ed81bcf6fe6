import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_constants.dart';
import '../../providers/providers.dart';
import '../../models/models.dart';
import '../../widgets/common/custom_error_widget.dart';

class PointagesManagementScreen extends StatefulWidget {
  const PointagesManagementScreen({super.key});

  @override
  State<PointagesManagementScreen> createState() => _PointagesManagementScreenState();
}

class _PointagesManagementScreenState extends State<PointagesManagementScreen> {
  final TextEditingController _searchController = TextEditingController();
  String? _selectedUserId;
  String? _selectedSiteId;
  DateTime? _dateFrom;
  DateTime? _dateTo;
  bool _showActiveOnly = false;

  @override
  void initState() {
    super.initState();
    _loadPointages();
  }

  void _loadPointages() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<AttendanceProvider>().loadPointagesWithFilters(
        userId: _selectedUserId,
        siteId: _selectedSiteId,
        dateFrom: _dateFrom,
        dateTo: _dateTo,
        activeOnly: _showActiveOnly,
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة سجلات الحضور'),
        backgroundColor: AppColors.background,
        foregroundColor: AppColors.textPrimary,
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: _showFilterDialog,
            tooltip: 'تصفية',
            color: AppColors.textPrimary,
          ),
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: _exportPointages,
            tooltip: 'تصدير Excel',
            color: AppColors.textPrimary,
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadPointages,
            tooltip: 'تحديث',
            color: AppColors.textPrimary,
          ),
        ],
      ),
      body: Column(
        children: [
          _buildSearchAndFilters(),
          _buildStatsHeader(),
          Expanded(child: _buildPointagesList()),
        ],
      ),
    );
  }

  Widget _buildSearchAndFilters() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      decoration: BoxDecoration(
        color: AppColors.background,
        boxShadow: [
          BoxShadow(
            color: AppColors.textPrimary.withOpacity(0.1),
            blurRadius: 5,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'البحث في سجلات الحضور...',
              prefixIcon: const Icon(Icons.search, color: AppColors.textPrimary),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(AppConstants.borderRadius),
              ),
              filled: true,
              fillColor: AppColors.background,
            ),
            onChanged: (value) {
              // Implement search functionality
            },
          ),
          if (_hasActiveFilters()) ...[
            const SizedBox(height: 12),
            _buildActiveFiltersChips(),
          ],
        ],
      ),
    );
  }

  Widget _buildStatsHeader() {
    return Consumer<AttendanceProvider>(
      builder: (context, provider, child) {
        final stats = provider.getPointagesStats();
        
        return Container(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'إجمالي السجلات',
                  '${stats['total'] ?? 0}',
                  Icons.list_alt,
                  AppColors.buttonText,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatCard(
                  'نشط حالياً',
                  '${stats['active'] ?? 0}',
                  Icons.access_time,
                  AppColors.buttonText,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatCard(
                  'مكتمل اليوم',
                  '${stats['completed_today'] ?? 0}',
                  Icons.check_circle,
                  AppColors.buttonText,
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppColors.buttonBackground,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: AppColors.buttonText.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: AppColors.buttonText, size: 20),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppColors.buttonText,
            ),
          ),
          const SizedBox(height: 2),
          Text(
            title,
            style: TextStyle(
              fontSize: 10,
              color: AppColors.textPrimary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildPointagesList() {
    return Consumer<AttendanceProvider>(
      builder: (context, provider, child) {
        if (provider.isLoading) {
          return const Center(child: CircularProgressIndicator(color: AppColors.buttonText));
        }

        if (provider.state == AttendanceState.error) {
          return CustomErrorWidget(
            message: provider.errorMessage ?? 'حدث خطأ في تحميل البيانات',
            onRetry: _loadPointages,
          );
        }

        final pointages = provider.pointages;

        if (pointages.isEmpty) {
          return _buildEmptyState();
        }

        return ListView.builder(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          itemCount: pointages.length,
          itemBuilder: (context, index) {
            final pointage = pointages[index];
            return _buildPointageCard(pointage);
          },
        );
      },
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.access_time_outlined,
            size: 100,
            color: AppColors.textPrimary,
          ),
          const SizedBox(height: 20),
          Text(
            'لا توجد سجلات حضور',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              color: AppColors.textPrimary,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'لم يتم العثور على سجلات حضور بالمعايير المحددة',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppColors.textPrimary,
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () {
              _clearFilters();
              _loadPointages();
            },
            icon: const Icon(Icons.clear_all, color: AppColors.textPrimary),
            label: const Text('مسح المرشحات', style: TextStyle(color: AppColors.textPrimary)),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.buttonBackground,
              foregroundColor: AppColors.buttonText,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPointageCard(Pointage pointage) {
    final isActive = pointage.finPointage == null;
    final duration = pointage.duree ?? _calculateCurrentDuration(pointage.debutPointage);

    return Card(
      margin: const EdgeInsets.only(bottom: AppConstants.defaultPadding),
      color: AppColors.background,
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                CircleAvatar(
                  backgroundColor: isActive ? AppColors.buttonText : AppColors.buttonText,
                  child: Icon(
                    isActive ? Icons.access_time : Icons.check,
                    color: AppColors.textPrimary,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        pointage.user?.name ?? 'غير محدد',
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        pointage.site?.name ?? 'غير محدد',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: AppColors.textPrimary,
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: AppColors.buttonBackground,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: AppColors.buttonText.withOpacity(0.3),
                    ),
                  ),
                  child: Text(
                    isActive ? 'نشط' : 'مكتمل',
                    style: TextStyle(
                      color: AppColors.buttonText,
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildPointageDetails(pointage, duration),
          ],
        ),
      ),
    );
  }

  Widget _buildPointageDetails(Pointage pointage, String duration) {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: _buildDetailItem(
                'بداية الحضور',
                _formatDateTime(pointage.debutPointage),
                Icons.login,
                AppColors.buttonText,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildDetailItem(
                'نهاية الحضور',
                pointage.finPointage != null 
                    ? _formatDateTime(pointage.finPointage!)
                    : 'لم ينته بعد',
                Icons.logout,
                pointage.finPointage != null ? AppColors.buttonText : AppColors.textPrimary,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        _buildDetailItem(
          'المدة الإجمالية',
          duration,
          Icons.timer,
          AppColors.buttonText,
        ),
      ],
    );
  }

  Widget _buildDetailItem(String label, String value, IconData icon, Color color) {
    return Row(
      children: [
        Icon(icon, size: 16, color: color),
        const SizedBox(width: 8),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              label,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: AppColors.textPrimary,
              ),
            ),
            Text(
              value,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w600,
                color: color,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildActiveFiltersChips() {
    return Wrap(
      spacing: 8,
      children: [
        if (_selectedUserId != null)
          _buildFilterChip('موظف محدد', () => setState(() => _selectedUserId = null)),
        if (_selectedSiteId != null)
          _buildFilterChip('موقع محدد', () => setState(() => _selectedSiteId = null)),
        if (_dateFrom != null)
          _buildFilterChip('من تاريخ', () => setState(() => _dateFrom = null)),
        if (_dateTo != null)
          _buildFilterChip('إلى تاريخ', () => setState(() => _dateTo = null)),
        if (_showActiveOnly)
          _buildFilterChip('نشط فقط', () => setState(() => _showActiveOnly = false)),
      ],
    );
  }

  Widget _buildFilterChip(String label, VoidCallback onRemove) {
    return Chip(
      label: Text(label, style: const TextStyle(color: AppColors.buttonText)),
      deleteIcon: const Icon(Icons.close, size: 16, color: AppColors.buttonText),
      onDeleted: onRemove,
      backgroundColor: AppColors.buttonBackground,
    );
  }

  bool _hasActiveFilters() {
    return _selectedUserId != null ||
           _selectedSiteId != null ||
           _dateFrom != null ||
           _dateTo != null ||
           _showActiveOnly;
  }

  void _clearFilters() {
    setState(() {
      _selectedUserId = null;
      _selectedSiteId = null;
      _dateFrom = null;
      _dateTo = null;
      _showActiveOnly = false;
    });
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  String _calculateCurrentDuration(DateTime start) {
    final now = DateTime.now();
    final difference = now.difference(start);
    final hours = difference.inHours;
    final minutes = difference.inMinutes % 60;
    return '${hours.toString().padLeft(2, '0')}:${minutes.toString().padLeft(2, '0')}:00';
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تصفية سجلات الحضور', style: TextStyle(color: AppColors.textPrimary)),
        backgroundColor: AppColors.background,
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // User filter
              DropdownButtonFormField<String>(
                initialValue: _selectedUserId,
                decoration: const InputDecoration(
                  labelText: 'الموظف',
                  border: OutlineInputBorder(),
                  labelStyle: TextStyle(color: AppColors.textPrimary),
                ),
                dropdownColor: AppColors.background,
                style: const TextStyle(color: AppColors.textPrimary),
                items: const [
                  DropdownMenuItem(value: null, child: Text('جميع الموظفين', style: TextStyle(color: AppColors.textPrimary))),
                  // Add more items from employees provider
                ],
                onChanged: (value) => setState(() => _selectedUserId = value),
              ),
              const SizedBox(height: 16),

              // Site filter
              DropdownButtonFormField<String>(
                initialValue: _selectedSiteId,
                decoration: const InputDecoration(
                  labelText: 'الموقع',
                  border: OutlineInputBorder(),
                  labelStyle: TextStyle(color: AppColors.textPrimary),
                ),
                dropdownColor: AppColors.background,
                style: const TextStyle(color: AppColors.textPrimary),
                items: const [
                  DropdownMenuItem(value: null, child: Text('جميع المواقع', style: TextStyle(color: AppColors.textPrimary))),
                  // Add more items from sites provider
                ],
                onChanged: (value) => setState(() => _selectedSiteId = value),
              ),
              const SizedBox(height: 16),

              // Date range
              Row(
                children: [
                  Expanded(
                    child: TextFormField(
                      decoration: const InputDecoration(
                        labelText: 'من تاريخ',
                        border: OutlineInputBorder(),
                        suffixIcon: Icon(Icons.calendar_today, color: AppColors.textPrimary),
                        labelStyle: TextStyle(color: AppColors.textPrimary),
                      ),
                      readOnly: true,
                      style: const TextStyle(color: AppColors.textPrimary),
                      onTap: () async {
                        final date = await showDatePicker(
                          context: context,
                          initialDate: _dateFrom ?? DateTime.now(),
                          firstDate: DateTime(2020),
                          lastDate: DateTime.now(),
                          builder: (context, child) => Theme(
                            data: Theme.of(context).copyWith(
                              colorScheme: const ColorScheme.light(
                                primary: AppColors.buttonText,
                                onPrimary: AppColors.textPrimary,
                              ),
                            ),
                            child: child!,
                          ),
                        );
                        if (date != null) {
                          setState(() => _dateFrom = date);
                        }
                      },
                      controller: TextEditingController(
                        text: _dateFrom?.toString().split(' ')[0] ?? '',
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: TextFormField(
                      decoration: const InputDecoration(
                        labelText: 'إلى تاريخ',
                        border: OutlineInputBorder(),
                        suffixIcon: Icon(Icons.calendar_today, color: AppColors.textPrimary),
                        labelStyle: TextStyle(color: AppColors.textPrimary),
                      ),
                      readOnly: true,
                      style: const TextStyle(color: AppColors.textPrimary),
                      onTap: () async {
                        final date = await showDatePicker(
                          context: context,
                          initialDate: _dateTo ?? DateTime.now(),
                          firstDate: DateTime(2020),
                          lastDate: DateTime.now(),
                          builder: (context, child) => Theme(
                            data: Theme.of(context).copyWith(
                              colorScheme: const ColorScheme.light(
                                primary: AppColors.buttonText,
                                onPrimary: AppColors.textPrimary,
                              ),
                            ),
                            child: child!,
                          ),
                        );
                        if (date != null) {
                          setState(() => _dateTo = date);
                        }
                      },
                      controller: TextEditingController(
                        text: _dateTo?.toString().split(' ')[0] ?? '',
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),

              // Active only filter
              CheckboxListTile(
                title: const Text('النشط فقط', style: TextStyle(color: AppColors.textPrimary)),
                value: _showActiveOnly,
                activeColor: AppColors.buttonText,
                checkColor: AppColors.textPrimary,
                onChanged: (value) => setState(() => _showActiveOnly = value ?? false),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء', style: TextStyle(color: AppColors.buttonText)),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _loadPointages();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.buttonBackground,
              foregroundColor: AppColors.buttonText,
            ),
            child: const Text('تطبيق'),
          ),
        ],
      ),
    );
  }

  void _exportPointages() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('سيتم تنفيذ تصدير Excel قريباً', style: TextStyle(color: AppColors.textPrimary)),
        backgroundColor: AppColors.buttonBackground,
      ),
    );
  }
}