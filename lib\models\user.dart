import 'package:json_annotation/json_annotation.dart';
import 'package:equatable/equatable.dart';

part 'user.g.dart';

@JsonSerializable()
class User extends Equatable {
  final int id;
  final String name;
  final String email;
  final String role;
  final String? phone;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'national_id')
  final String? nationalId;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'employee_id')
  final String? employeeId;
  final String? department;
  final String? position;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'birth_date')
  final String? birthDate;
  final double? salary;
  @J<PERSON><PERSON><PERSON>(name: 'default_site_id')
  final int? defaultSiteId;
  @J<PERSON><PERSON><PERSON>(name: 'created_at')
  final String? createdAt;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'updated_at')
  final String? updatedAt;

  const User({
    required this.id,
    required this.name,
    required this.email,
    required this.role,
    this.phone,
    this.nationalId,
    this.employeeId,
    this.department,
    this.position,
    this.birthDate,
    this.salary,
    this.defaultSiteId,
    this.createdAt,
    this.updatedAt,
  });

  factory User.from<PERSON><PERSON>(Map<String, dynamic> json) => _$UserFromJson(json);

  Map<String, dynamic> toJson() => _$UserToJson(this);

  User copyWith({
    int? id,
    String? name,
    String? email,
    String? role,
    String? phone,
    String? nationalId,
    String? employeeId,
    String? department,
    String? position,
    String? birthDate,
    double? salary,
    int? defaultSiteId,
    String? createdAt,
    String? updatedAt,
  }) {
    return User(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      role: role ?? this.role,
      phone: phone ?? this.phone,
      nationalId: nationalId ?? this.nationalId,
      employeeId: employeeId ?? this.employeeId,
      department: department ?? this.department,
      position: position ?? this.position,
      birthDate: birthDate ?? this.birthDate,
      salary: salary ?? this.salary,
      defaultSiteId: defaultSiteId ?? this.defaultSiteId,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  bool get isAdmin => role == 'admin';
  bool get isEmployee => role == 'employee';

  String get displayName => name;
  String get displayRole => isAdmin ? 'مدير' : 'موظف';

  @override
  List<Object?> get props => [
    id, name, email, role, phone, nationalId, employeeId,
    department, position, birthDate, salary, defaultSiteId,
    createdAt, updatedAt
  ];

  @override
  String toString() {
    return 'User{id: $id, name: $name, email: $email, role: $role}';
  }
}

@JsonSerializable()
class UserCreateRequest extends Equatable {
  final String name;
  final String email;
  final String password;
  final String role;

  const UserCreateRequest({
    required this.name,
    required this.email,
    required this.password,
    required this.role,
  });

  factory UserCreateRequest.fromJson(Map<String, dynamic> json) =>
      _$UserCreateRequestFromJson(json);

  Map<String, dynamic> toJson() => _$UserCreateRequestToJson(this);

  @override
  List<Object?> get props => [name, email, password, role];
}

@JsonSerializable()
class UserUpdateRequest extends Equatable {
  final String? name;
  final String? email;
  final String? password;
  final String? role;

  const UserUpdateRequest({
    this.name,
    this.email,
    this.password,
    this.role,
  });

  factory UserUpdateRequest.fromJson(Map<String, dynamic> json) =>
      _$UserUpdateRequestFromJson(json);

  Map<String, dynamic> toJson() => _$UserUpdateRequestToJson(this);

  @override
  List<Object?> get props => [name, email, password, role];
}

@JsonSerializable()
class LoginRequest extends Equatable {
  final String email;
  final String password;

  const LoginRequest({
    required this.email,
    required this.password,
  });

  factory LoginRequest.fromJson(Map<String, dynamic> json) =>
      _$LoginRequestFromJson(json);

  Map<String, dynamic> toJson() => _$LoginRequestToJson(this);

  @override
  List<Object?> get props => [email, password];
}

@JsonSerializable()
class LoginResponse extends Equatable {
  final User user;
  final String token;

  const LoginResponse({
    required this.user,
    required this.token,
  });

  factory LoginResponse.fromJson(Map<String, dynamic> json) =>
      _$LoginResponseFromJson(json);

  Map<String, dynamic> toJson() => _$LoginResponseToJson(this);

  @override
  List<Object?> get props => [user, token];
}

@JsonSerializable()
class ChangePasswordRequest extends Equatable {
  @JsonKey(name: 'current_password')
  final String currentPassword;
  @JsonKey(name: 'new_password')
  final String newPassword;
  @JsonKey(name: 'new_password_confirmation')
  final String newPasswordConfirmation;

  const ChangePasswordRequest({
    required this.currentPassword,
    required this.newPassword,
    required this.newPasswordConfirmation,
  });

  factory ChangePasswordRequest.fromJson(Map<String, dynamic> json) =>
      _$ChangePasswordRequestFromJson(json);

  Map<String, dynamic> toJson() => _$ChangePasswordRequestToJson(this);

  @override
  List<Object?> get props => [currentPassword, newPassword, newPasswordConfirmation];
}
