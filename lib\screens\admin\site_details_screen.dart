import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_constants.dart';
import '../../providers/providers.dart';
import '../../models/models.dart';
import '../../routes/app_routes.dart';
import '../../widgets/admin/site_assignments_widget.dart';

class SiteDetailsScreen extends StatefulWidget {
  final int siteId;

  const SiteDetailsScreen({
    super.key,
    required this.siteId,
  });

  @override
  State<SiteDetailsScreen> createState() => _SiteDetailsScreenState();
}

class _SiteDetailsScreenState extends State<SiteDetailsScreen> {
  Site? _site;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadSiteDetails();
  }

  Future<void> _loadSiteDetails() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final sitesProvider = Provider.of<SitesProvider>(context, listen: false);

      final site = await sitesProvider.getSite(widget.siteId);

      if (site != null) {
        setState(() {
          _site = site;
          _isLoading = false;
        });
      } else {
        throw Exception('Site not found');
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل تفاصيل الموقع: ${e.toString()}'),
            backgroundColor: AppColors.textPrimary
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: Text(_site?.name ?? 'تفاصيل الموقع'),
        backgroundColor: AppColors.background,
        foregroundColor: AppColors.textPrimary,
        elevation: 0,
        actions: [
          if (_site != null) ...[
            IconButton(
              icon: const Icon(Icons.people),
              color: AppColors.textPrimary,
              onPressed: () => _navigateToAssignment(),
              tooltip: 'تعيين الموظفين',
            ),
            IconButton(
              icon: const Icon(Icons.edit),
              color: AppColors.textPrimary,
              onPressed: () => _editSite(),
              tooltip: 'تعديل الموقع',
            ),
            IconButton(
              icon: const Icon(Icons.delete),
              color: AppColors.buttonText,
              onPressed: () => _showDeleteDialog(),
              tooltip: 'حذف الموقع',
            ),
          ],
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator(color: AppColors.textPrimary))
          : _site == null
              ? _buildErrorView()
              : _buildSiteDetails(),
    );
  }

  Widget _buildErrorView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: AppColors.buttonText,
          ),
          const SizedBox(height: 16),
          Text(
            'لم يتم العثور على الموقع',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              color: AppColors.buttonText,
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.buttonBackground,
              foregroundColor: AppColors.buttonText,
            ),
            onPressed: () => Navigator.pop(context),
            child: const Text('العودة'),
          ),
        ],
      ),
    );
  }

  Widget _buildSiteDetails() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildInfoCard(),
          const SizedBox(height: AppConstants.defaultPadding),
          SiteAssignmentsWidget(
            site: _site!,
            onAssignmentChanged: _loadSiteDetails,
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          _buildMapCard(),
        ],
      ),
    );
  }

  Widget _buildInfoCard() {
    return Card(
      color: AppColors.background,
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.business,
                  color: AppColors.textPrimary,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  'معلومات الموقع',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppColors.textPrimary,
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            _buildInfoRow('اسم الموقع', _site!.name),
            const SizedBox(height: 8),
            _buildInfoRow('تاريخ الإنشاء', _formatDate(_site!.createdAt)),
            if (_site!.updatedAt != null) ...[
              const SizedBox(height: 8),
              _buildInfoRow('آخر تحديث', _formatDate(_site!.updatedAt)),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildMapCard() {
    return Card(
      color: AppColors.background,
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.map,
                  color: AppColors.textPrimary,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  'الموقع على الخريطة',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppColors.textPrimary,
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            Container(
              height: 200,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(AppConstants.borderRadius),
                border: Border.all(color: AppColors.background),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(AppConstants.borderRadius),
                child: GoogleMap(
                  initialCameraPosition: CameraPosition(
                    target: LatLng(_site!.latitude, _site!.longitude),
                    zoom: 16,
                  ),
                  onMapCreated: (GoogleMapController controller) {
                    // Map controller ready
                  },
                  markers: {
                    Marker(
                      markerId: MarkerId('site_${_site!.id}'),
                      position: LatLng(_site!.latitude, _site!.longitude),
                      infoWindow: InfoWindow(
                        title: _site!.name,
                        snippet: 'موقع العمل',
                      ),
                      icon: BitmapDescriptor.defaultMarkerWithHue(
                        BitmapDescriptor.hueBlue,
                      ),
                    ),
                  },
                  mapType: MapType.normal,
                  zoomControlsEnabled: true,
                  scrollGesturesEnabled: true,
                  zoomGesturesEnabled: true,
                  rotateGesturesEnabled: false,
                  tiltGesturesEnabled: false,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }


  Widget _buildStatItem(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 32),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              color: AppColors.textPrimary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String? value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 100,
          child: Text(
            '$label:',
            style: TextStyle(
              fontWeight: FontWeight.w600,
              color: AppColors.textPrimary,
            ),
          ),
        ),
        Expanded(
          child: Text(
            value ?? 'غير محدد',
            style: TextStyle(
              color: AppColors.textPrimary,
            ),
          ),
        ),
      ],
    );
  }

  String _formatDate(String? dateString) {
    if (dateString == null) return 'غير محدد';
    
    try {
      final date = DateTime.parse(dateString);
      return '${date.day}/${date.month}/${date.year}';
    } catch (e) {
      return dateString;
    }
  }

  void _navigateToAssignment() {
    Navigator.pushNamed(
      context,
      AppRoutes.siteAssignment,
      arguments: {'site': _site},
    ).then((result) {
      if (result == true) {
        _loadSiteDetails();
      }
    });
  }

  void _editSite() {
    Navigator.pushNamed(
      context,
      AppRoutes.editSite,
      arguments: {'site': _site},
    ).then((result) {
      if (result == true) {
        _loadSiteDetails();
      }
    });
  }

  void _showDeleteDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppColors.background,
        title: Text('تأكيد الحذف', style: TextStyle(color: AppColors.textPrimary)),
        content: Text('هل أنت متأكد من حذف الموقع "${_site!.name}"؟\n\nسيتم حذف جميع البيانات المرتبطة بهذا الموقع.', style: TextStyle(color: AppColors.textPrimary)),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('إلغاء', style: TextStyle(color: AppColors.buttonText)),
          ),
          ElevatedButton(
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.buttonBackground,
              foregroundColor: AppColors.buttonText,
            ),
            onPressed: () {
              Navigator.pop(context);
              _deleteSite();
            },
            child: Text('حذف'),
          ),
        ],
      ),
    );
  }

  void _deleteSite() async {
    try {
      final siteProvider = Provider.of<SitesProvider>(context, listen: false);
      await siteProvider.deleteSite(_site!.id);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم حذف الموقع بنجاح'),
            backgroundColor: AppColors.buttonText,
          ),
        );
        Navigator.pop(context, true);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل في حذف الموقع: ${e.toString()}'),
            backgroundColor: AppColors.buttonText,
          ),
        );
      }
    }
  }
}