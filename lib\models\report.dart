import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'report.g.dart';

@JsonSerializable()
class ReportResponse extends Equatable {
  final String filename;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'download_url')
  final String downloadUrl;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'file_size')
  final String fileSize;
  @Json<PERSON>ey(name: 'generated_at')
  final String generatedAt;
  final ReportPeriod? period;
  @JsonKey(name: 'user_id')
  final int? userId;
  @Json<PERSON>ey(name: 'site_id')
  final int? siteId;
  @Json<PERSON>ey(name: 'test_mode')
  final bool? testMode;

  const ReportResponse({
    required this.filename,
    required this.downloadUrl,
    required this.fileSize,
    required this.generatedAt,
    this.period,
    this.userId,
    this.siteId,
    this.testMode,
  });

  factory ReportResponse.fromJson(Map<String, dynamic> json) =>
      _$ReportResponseFromJson(json);

  Map<String, dynamic> toJson() => _$ReportResponseToJson(this);

  @override
  List<Object?> get props => [
        filename,
        downloadUrl,
        fileSize,
        generatedAt,
        period,
        userId,
        siteId,
        testMode,
      ];
}

@JsonSerializable()
class ReportPeriod extends Equatable {
  final String start;
  final String end;

  const ReportPeriod({
    required this.start,
    required this.end,
  });

  factory ReportPeriod.fromJson(Map<String, dynamic> json) =>
      _$ReportPeriodFromJson(json);

  Map<String, dynamic> toJson() => _$ReportPeriodToJson(this);

  @override
  List<Object?> get props => [start, end];
}

@JsonSerializable()
class PresenceVerificationResponse extends Equatable {
  @JsonKey(name: 'is_present')
  final bool isPresent;
  final double distance;
  @JsonKey(name: 'max_distance')
  final double maxDistance;
  final VerificationSite? site;
  final String status;
  @JsonKey(name: 'verification_time')
  final String verificationTime;
  final String message;

  const PresenceVerificationResponse({
    required this.isPresent,
    required this.distance,
    required this.maxDistance,
    this.site,
    required this.status,
    required this.verificationTime,
    required this.message,
  });

  factory PresenceVerificationResponse.fromJson(Map<String, dynamic> json) =>
      _$PresenceVerificationResponseFromJson(json);

  Map<String, dynamic> toJson() => _$PresenceVerificationResponseToJson(this);

  @override
  List<Object?> get props => [
        isPresent,
        distance,
        maxDistance,
        site,
        status,
        verificationTime,
        message,
      ];
}

@JsonSerializable()
class VerificationSite extends Equatable {
  final int id;
  final String name;
  final double latitude;
  final double longitude;

  const VerificationSite({
    required this.id,
    required this.name,
    required this.latitude,
    required this.longitude,
  });

  factory VerificationSite.fromJson(Map<String, dynamic> json) =>
      _$VerificationSiteFromJson(json);

  Map<String, dynamic> toJson() => _$VerificationSiteToJson(this);

  @override
  List<Object?> get props => [id, name, latitude, longitude];
}

@JsonSerializable()
class AllEmployeesCheckResponse extends Equatable {
  @JsonKey(name: 'total_checked')
  final int totalChecked;
  @JsonKey(name: 'present_count')
  final int presentCount;
  @JsonKey(name: 'absent_count')
  final int absentCount;
  final List<EmployeeVerification> verifications;

  const AllEmployeesCheckResponse({
    required this.totalChecked,
    required this.presentCount,
    required this.absentCount,
    required this.verifications,
  });

  factory AllEmployeesCheckResponse.fromJson(Map<String, dynamic> json) =>
      _$AllEmployeesCheckResponseFromJson(json);

  Map<String, dynamic> toJson() => _$AllEmployeesCheckResponseToJson(this);

  @override
  List<Object?> get props => [
        totalChecked,
        presentCount,
        absentCount,
        verifications,
      ];
}

@JsonSerializable()
class EmployeeVerification extends Equatable {
  final EmployeeInfo employee;
  final PresenceVerificationResponse verification;

  const EmployeeVerification({
    required this.employee,
    required this.verification,
  });

  factory EmployeeVerification.fromJson(Map<String, dynamic> json) =>
      _$EmployeeVerificationFromJson(json);

  Map<String, dynamic> toJson() => _$EmployeeVerificationToJson(this);

  @override
  List<Object?> get props => [employee, verification];
}

@JsonSerializable()
class EmployeeInfo extends Equatable {
  final int id;
  final String name;
  final String email;
  final String? role;

  const EmployeeInfo({
    required this.id,
    required this.name,
    required this.email,
    this.role,
  });

  factory EmployeeInfo.fromJson(Map<String, dynamic> json) =>
      _$EmployeeInfoFromJson(json);

  Map<String, dynamic> toJson() => _$EmployeeInfoToJson(this);

  @override
  List<Object?> get props => [id, name, email, role];
}

@JsonSerializable()
class PresenceVerificationRequest extends Equatable {
  @JsonKey(name: 'user_id')
  final int userId;
  final double latitude;
  final double longitude;

  const PresenceVerificationRequest({
    required this.userId,
    required this.latitude,
    required this.longitude,
  });

  factory PresenceVerificationRequest.fromJson(Map<String, dynamic> json) =>
      _$PresenceVerificationRequestFromJson(json);

  Map<String, dynamic> toJson() => _$PresenceVerificationRequestToJson(this);

  @override
  List<Object?> get props => [userId, latitude, longitude];
}