import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_constants.dart';
import '../../providers/auth_provider.dart';

class EditProfileScreen extends StatefulWidget {
  const EditProfileScreen({super.key});

  @override
  State<EditProfileScreen> createState() => _EditProfileScreenState();
}

class _EditProfileScreenState extends State<EditProfileScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _phoneController = TextEditingController();
  final _nationalIdController = TextEditingController();
  final _departmentController = TextEditingController();
  final _positionController = TextEditingController();

  DateTime? _selectedBirthDate;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadUserData();
  }

  void _loadUserData() {
    final user = context.read<AuthProvider>().user;
    if (user != null) {
      _nameController.text = user.name;
      _phoneController.text = user.phone ?? '';
      _nationalIdController.text = user.nationalId ?? '';
      _departmentController.text = user.department ?? '';
      _positionController.text = user.position ?? '';

      if (user.birthDate != null) {
        try {
          _selectedBirthDate = DateTime.parse(user.birthDate!);
        } catch (e) {
          // Handle parsing error
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('تعديل الملف الشخصي'),
        backgroundColor: AppColors.background,
        foregroundColor: AppColors.textPrimary,
        actions: [
          IconButton(
            icon: const Icon(Icons.save),
            color: AppColors.textPrimary,
            onPressed: _isLoading ? null : _saveProfile,
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Form(
          key: _formKey,
          child: Column(
            children: [
              _buildProfileHeader(),
              const SizedBox(height: AppConstants.largePadding),
              _buildPersonalInfoSection(),
              const SizedBox(height: AppConstants.defaultPadding),
              _buildWorkInfoSection(),
              const SizedBox(height: AppConstants.largePadding),
              _buildActionButtons(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildProfileHeader() {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        final user = authProvider.user;
        return Container(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          decoration: BoxDecoration(
            color: AppColors.background,
            borderRadius: BorderRadius.circular(AppConstants.borderRadius),
          ),
          child: Column(
            children: [
              Stack(
                children: [
                  CircleAvatar(
                    radius: 50,
                    backgroundColor: AppColors.textPrimary.withOpacity(0.2),
                    child: Text(
                      user?.name.isNotEmpty == true
                          ? user!.name[0].toUpperCase()
                          : 'U',
                      style: const TextStyle(
                        color: AppColors.textPrimary,
                        fontSize: 36,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  Positioned(
                    bottom: 0,
                    right: 0,
                    child: Container(
                      padding: const EdgeInsets.all(4),
                      decoration: const BoxDecoration(
                        color: AppColors.textPrimary,
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        Icons.camera_alt,
                        color: AppColors.buttonText,
                        size: 20,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: AppConstants.defaultPadding),
              Text(
                user?.name ?? 'المستخدم',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  color: AppColors.textPrimary,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                user?.email ?? '',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: AppColors.textPrimary.withOpacity(0.9),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildPersonalInfoSection() {
    return Card(
      color: AppColors.background,
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'المعلومات الشخصية',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppColors.buttonText,
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            TextFormField(
              controller: _nameController,
              decoration: InputDecoration(
                labelText: 'الاسم الكامل *',
                prefixIcon: Icon(Icons.person, color: AppColors.textPrimary),
                border: const OutlineInputBorder(),
                labelStyle: TextStyle(color: AppColors.textPrimary),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'الاسم مطلوب';
                }
                if (value.length < 2) {
                  return 'الاسم يجب أن يكون أكثر من حرفين';
                }
                return null;
              },
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            TextFormField(
              controller: _phoneController,
              decoration: InputDecoration(
                labelText: 'رقم الهاتف',
                prefixIcon: Icon(Icons.phone, color: AppColors.textPrimary),
                border: const OutlineInputBorder(),
                labelStyle: TextStyle(color: AppColors.textPrimary),
              ),
              keyboardType: TextInputType.phone,
              validator: (value) {
                if (value != null && value.isNotEmpty) {
                  if (!RegExp(r'^[0-9+\-\s()]+$').hasMatch(value)) {
                    return 'رقم الهاتف غير صحيح';
                  }
                }
                return null;
              },
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            TextFormField(
              controller: _nationalIdController,
              decoration: InputDecoration(
                labelText: 'رقم الهوية',
                prefixIcon: Icon(Icons.badge, color: AppColors.textPrimary),
                border: const OutlineInputBorder(),
                labelStyle: TextStyle(color: AppColors.textPrimary),
              ),
              keyboardType: TextInputType.number,
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            InkWell(
              onTap: _selectBirthDate,
              child: InputDecorator(
                decoration: InputDecoration(
                  labelText: 'تاريخ الميلاد',
                  prefixIcon: Icon(Icons.calendar_today, color: AppColors.textPrimary),
                  border: const OutlineInputBorder(),
                  labelStyle: TextStyle(color: AppColors.textPrimary),
                ),
                child: Text(
                  _selectedBirthDate != null
                      ? DateFormat('dd/MM/yyyy').format(_selectedBirthDate!)
                      : 'اختر تاريخ الميلاد',
                  style: TextStyle(
                    color: _selectedBirthDate != null
                        ? AppColors.textPrimary
                        : AppColors.textPrimary.withOpacity(0.5),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildWorkInfoSection() {
    return Card(
      color: AppColors.background,
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'معلومات العمل',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppColors.buttonText,
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            TextFormField(
              controller: _departmentController,
              decoration: InputDecoration(
                labelText: 'القسم',
                prefixIcon: Icon(Icons.business, color: AppColors.textPrimary),
                border: const OutlineInputBorder(),
                labelStyle: TextStyle(color: AppColors.textPrimary),
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            TextFormField(
              controller: _positionController,
              decoration: InputDecoration(
                labelText: 'المنصب',
                prefixIcon: Icon(Icons.work, color: AppColors.textPrimary),
                border: const OutlineInputBorder(),
                labelStyle: TextStyle(color: AppColors.textPrimary),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton(
            onPressed: _isLoading ? null : () => Navigator.pop(context),
            style: OutlinedButton.styleFrom(
              foregroundColor: AppColors.buttonText,
              side: BorderSide(color: AppColors.buttonText),
            ),
            child: const Text('إلغاء'),
          ),
        ),
        const SizedBox(width: AppConstants.defaultPadding),
        Expanded(
          child: ElevatedButton(
            onPressed: _isLoading ? null : _saveProfile,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.buttonBackground,
              foregroundColor: AppColors.buttonText,
            ),
            child: _isLoading
                ? const SizedBox(
                    height: 20,
                    width: 20,
                    child: CircularProgressIndicator(strokeWidth: 2, color: AppColors.buttonText),
                  )
                : const Text('حفظ التغييرات'),
          ),
        ),
      ],
    );
  }

  void _selectBirthDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate:
          _selectedBirthDate ??
          DateTime.now().subtract(const Duration(days: 365 * 25)),
      firstDate: DateTime(1950),
      lastDate: DateTime.now(),
    );

    if (picked != null && picked != _selectedBirthDate) {
      setState(() {
        _selectedBirthDate = picked;
      });
    }
  }

  void _saveProfile() async {
    if (_formKey.currentState?.validate() ?? false) {
      setState(() {
        _isLoading = true;
      });

      try {
        final authProvider = context.read<AuthProvider>();

        await authProvider.updateProfile(
          name: _nameController.text,
          email: authProvider.user?.email, // Keep existing email
        );

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم حفظ التغييرات بنجاح'),
              backgroundColor: AppColors.buttonText,
            ),
          );
          Navigator.pop(context);
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('فشل في حفظ التغييرات: $e'),
              backgroundColor: AppColors.buttonText,
            ),
          );
        }
      } finally {
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }
      }
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _phoneController.dispose();
    _nationalIdController.dispose();
    _departmentController.dispose();
    _positionController.dispose();
    super.dispose();
  }
}