// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'monitoring.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

EmployeeMonitoringResponse _$EmployeeMonitoringResponseFromJson(
        Map<String, dynamic> json) =>
    EmployeeMonitoringResponse(
      employee: Employee.from<PERSON>son(json['employee'] as Map<String, dynamic>),
      assignedSite:
          AssignedSite.fromJson(json['assigned_site'] as Map<String, dynamic>),
      currentPosition: CurrentPosition.fromJson(
          json['current_position'] as Map<String, dynamic>),
      isOnAssignedSite: json['is_on_assigned_site'] as bool,
      distanceFromSite: (json['distance_from_site'] as num).toDouble(),
      maxAllowedDistance: (json['max_allowed_distance'] as num).toDouble(),
      presenceStatus: json['presence_status'] as String,
      checkTime: DateTime.parse(json['check_time'] as String),
      message: json['message'] as String,
      messageAr: json['message_ar'] as String,
    );

Map<String, dynamic> _$EmployeeMonitoringResponseToJson(
        EmployeeMonitoringResponse instance) =>
    <String, dynamic>{
      'employee': instance.employee,
      'assigned_site': instance.assignedSite,
      'current_position': instance.currentPosition,
      'is_on_assigned_site': instance.isOnAssignedSite,
      'distance_from_site': instance.distanceFromSite,
      'max_allowed_distance': instance.maxAllowedDistance,
      'presence_status': instance.presenceStatus,
      'check_time': instance.checkTime.toIso8601String(),
      'message': instance.message,
      'message_ar': instance.messageAr,
    };

Employee _$EmployeeFromJson(Map<String, dynamic> json) => Employee(
      id: (json['id'] as num).toInt(),
      name: json['name'] as String,
      email: json['email'] as String,
      role: json['role'] as String,
    );

Map<String, dynamic> _$EmployeeToJson(Employee instance) => <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'email': instance.email,
      'role': instance.role,
    };

AssignedSite _$AssignedSiteFromJson(Map<String, dynamic> json) => AssignedSite(
      id: (json['id'] as num).toInt(),
      name: json['name'] as String,
      latitude: (json['latitude'] as num).toDouble(),
      longitude: (json['longitude'] as num).toDouble(),
    );

Map<String, dynamic> _$AssignedSiteToJson(AssignedSite instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'latitude': instance.latitude,
      'longitude': instance.longitude,
    };

CurrentPosition _$CurrentPositionFromJson(Map<String, dynamic> json) =>
    CurrentPosition(
      latitude: (json['latitude'] as num).toDouble(),
      longitude: (json['longitude'] as num).toDouble(),
    );

Map<String, dynamic> _$CurrentPositionToJson(CurrentPosition instance) =>
    <String, dynamic>{
      'latitude': instance.latitude,
      'longitude': instance.longitude,
    };

AllEmployeesMonitoringResponse _$AllEmployeesMonitoringResponseFromJson(
        Map<String, dynamic> json) =>
    AllEmployeesMonitoringResponse(
      summary:
          MonitoringSummary.fromJson(json['summary'] as Map<String, dynamic>),
      detailedResults: (json['detailed_results'] as List<dynamic>)
          .map((e) => DetailedResult.fromJson(e as Map<String, dynamic>))
          .toList(),
      checkTime: DateTime.parse(json['check_time'] as String),
    );

Map<String, dynamic> _$AllEmployeesMonitoringResponseToJson(
        AllEmployeesMonitoringResponse instance) =>
    <String, dynamic>{
      'summary': instance.summary,
      'detailed_results': instance.detailedResults,
      'check_time': instance.checkTime.toIso8601String(),
    };

MonitoringSummary _$MonitoringSummaryFromJson(Map<String, dynamic> json) =>
    MonitoringSummary(
      totalChecked: (json['total_checked'] as num).toInt(),
      presentOnSite: (json['present_on_site'] as num).toInt(),
      absentFromSite: (json['absent_from_site'] as num).toInt(),
      noAssignedSite: (json['no_assigned_site'] as num).toInt(),
      notificationsSent: (json['notifications_sent'] as num).toInt(),
    );

Map<String, dynamic> _$MonitoringSummaryToJson(MonitoringSummary instance) =>
    <String, dynamic>{
      'total_checked': instance.totalChecked,
      'present_on_site': instance.presentOnSite,
      'absent_from_site': instance.absentFromSite,
      'no_assigned_site': instance.noAssignedSite,
      'notifications_sent': instance.notificationsSent,
    };

DetailedResult _$DetailedResultFromJson(Map<String, dynamic> json) =>
    DetailedResult(
      pointageId: (json['pointage_id'] as num).toInt(),
      startedAt: DateTime.parse(json['started_at'] as String),
      checkResult:
          CheckResult.fromJson(json['check_result'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$DetailedResultToJson(DetailedResult instance) =>
    <String, dynamic>{
      'pointage_id': instance.pointageId,
      'started_at': instance.startedAt.toIso8601String(),
      'check_result': instance.checkResult,
    };

CheckResult _$CheckResultFromJson(Map<String, dynamic> json) => CheckResult(
      employee: Employee.fromJson(json['employee'] as Map<String, dynamic>),
      isOnAssignedSite: json['is_on_assigned_site'] as bool,
      distanceFromSite: (json['distance_from_site'] as num).toDouble(),
    );

Map<String, dynamic> _$CheckResultToJson(CheckResult instance) =>
    <String, dynamic>{
      'employee': instance.employee,
      'is_on_assigned_site': instance.isOnAssignedSite,
      'distance_from_site': instance.distanceFromSite,
    };

MonitoringControlResponse _$MonitoringControlResponseFromJson(
        Map<String, dynamic> json) =>
    MonitoringControlResponse(
      message: json['message'] as String,
      messageAr: json['message_ar'] as String?,
      monitoringConfig: json['monitoring_config'] == null
          ? null
          : MonitoringConfig.fromJson(
              json['monitoring_config'] as Map<String, dynamic>),
      monitoringSummary: json['monitoring_summary'] == null
          ? null
          : MonitoringSummaryControl.fromJson(
              json['monitoring_summary'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$MonitoringControlResponseToJson(
        MonitoringControlResponse instance) =>
    <String, dynamic>{
      'message': instance.message,
      'message_ar': instance.messageAr,
      'monitoring_config': instance.monitoringConfig,
      'monitoring_summary': instance.monitoringSummary,
    };

MonitoringConfig _$MonitoringConfigFromJson(Map<String, dynamic> json) =>
    MonitoringConfig(
      userId: (json['user_id'] as num).toInt(),
      intervalMinutes: (json['interval_minutes'] as num).toInt(),
      startedAt: DateTime.parse(json['started_at'] as String),
      expiresAt: DateTime.parse(json['expires_at'] as String),
    );

Map<String, dynamic> _$MonitoringConfigToJson(MonitoringConfig instance) =>
    <String, dynamic>{
      'user_id': instance.userId,
      'interval_minutes': instance.intervalMinutes,
      'started_at': instance.startedAt.toIso8601String(),
      'expires_at': instance.expiresAt.toIso8601String(),
    };

MonitoringSummaryControl _$MonitoringSummaryControlFromJson(
        Map<String, dynamic> json) =>
    MonitoringSummaryControl(
      durationMinutes: (json['duration_minutes'] as num).toInt(),
      stoppedAt: DateTime.parse(json['stopped_at'] as String),
    );

Map<String, dynamic> _$MonitoringSummaryControlToJson(
        MonitoringSummaryControl instance) =>
    <String, dynamic>{
      'duration_minutes': instance.durationMinutes,
      'stopped_at': instance.stoppedAt.toIso8601String(),
    };

MonitoringStatusResponse _$MonitoringStatusResponseFromJson(
        Map<String, dynamic> json) =>
    MonitoringStatusResponse(
      activeMonitoring: (json['active_monitoring'] as List<dynamic>)
          .map((e) => ActiveMonitoring.fromJson(e as Map<String, dynamic>))
          .toList(),
      totalMonitored: (json['total_monitored'] as num).toInt(),
    );

Map<String, dynamic> _$MonitoringStatusResponseToJson(
        MonitoringStatusResponse instance) =>
    <String, dynamic>{
      'active_monitoring': instance.activeMonitoring,
      'total_monitored': instance.totalMonitored,
    };

ActiveMonitoring _$ActiveMonitoringFromJson(Map<String, dynamic> json) =>
    ActiveMonitoring(
      userId: (json['user_id'] as num).toInt(),
      userName: json['user_name'] as String,
      startedAt: DateTime.parse(json['started_at'] as String),
      intervalMinutes: (json['interval_minutes'] as num).toInt(),
      expiresAt: DateTime.parse(json['expires_at'] as String),
    );

Map<String, dynamic> _$ActiveMonitoringToJson(ActiveMonitoring instance) =>
    <String, dynamic>{
      'user_id': instance.userId,
      'user_name': instance.userName,
      'started_at': instance.startedAt.toIso8601String(),
      'interval_minutes': instance.intervalMinutes,
      'expires_at': instance.expiresAt.toIso8601String(),
    };
