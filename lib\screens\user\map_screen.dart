import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:geolocator/geolocator.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_constants.dart';
import '../../providers/providers.dart';
import '../../services/location_service.dart';
import '../../models/models.dart';

class MapScreen extends StatefulWidget {
  const MapScreen({super.key});

  @override
  State<MapScreen> createState() => _MapScreenState();
}

class _MapScreenState extends State<MapScreen> {
  GoogleMapController? _mapController;
  Position? _currentPosition;
  bool _isLoadingLocation = false;
  final Set<Marker> _markers = {};
  final Set<Circle> _circles = {};
  Site? _selectedSite;

  @override
  void initState() {
    super.initState();
    _getCurrentLocation();
    _loadSites();
  }

  void _getCurrentLocation() async {
    setState(() {
      _isLoadingLocation = true;
    });

    try {
      final position = await LocationService().getCurrentPosition();
      setState(() {
        _currentPosition = position;
      });
      _updateMapLocation();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('فشل في تحديد الموقع: $e', style: TextStyle(color: AppColors.textPrimary))),
        );
      }
    } finally {
      setState(() {
        _isLoadingLocation = false;
      });
    }
  }

  void _loadSites() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<SitesProvider>().loadSitesData();
    });
  }

  void _updateMapLocation() {
    if (_currentPosition != null && _mapController != null) {
      _mapController!.animateCamera(
        CameraUpdate.newLatLngZoom(
          LatLng(_currentPosition!.latitude, _currentPosition!.longitude),
          AppConstants.defaultZoom,
        ),
      );
    }
    _updateMarkers();
  }

  void _updateMarkers() {
    _markers.clear();
    _circles.clear();

    // Add current location marker
    if (_currentPosition != null) {
      _markers.add(
        Marker(
          markerId: const MarkerId('current_location'),
          position: LatLng(_currentPosition!.latitude, _currentPosition!.longitude),
          icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueBlue),
          infoWindow: const InfoWindow(
            title: 'موقعك الحالي',
            snippet: 'أنت هنا',
          ),
        ),
      );

      // Add accuracy circle
      _circles.add(
        Circle(
          circleId: const CircleId('accuracy'),
          center: LatLng(_currentPosition!.latitude, _currentPosition!.longitude),
          radius: _currentPosition!.accuracy,
          fillColor: AppColors.background.withOpacity(0.2),
          strokeColor: AppColors.background,
          strokeWidth: 2,
        ),
      );
    }

    // Add site markers
    final sites = context.read<SitesProvider>().sites;
    for (final site in sites) {
      if (site.latitude != 0 && site.longitude != 0) {
        final isNearby = _currentPosition != null &&
            _calculateDistance(
              _currentPosition!.latitude,
              _currentPosition!.longitude,
              site.latitude,
              site.longitude,
            ) <= AppConstants.maxAllowedDistance;

        _markers.add(
          Marker(
            markerId: MarkerId('site_${site.id}'),
            position: LatLng(site.latitude, site.longitude),
            icon: BitmapDescriptor.defaultMarkerWithHue(
              isNearby ? BitmapDescriptor.hueGreen : BitmapDescriptor.hueRed,
            ),
            infoWindow: InfoWindow(
              title: site.name,
              snippet: isNearby ? 'في النطاق المسموح' : 'خارج النطاق',
              onTap: () => _showSiteDetails(site),
            ),
            onTap: () => _selectSite(site),
          ),
        );

        // Add site range circle
        _circles.add(
          Circle(
            circleId: CircleId('site_${site.id}'),
            center: LatLng(site.latitude, site.longitude),
            radius: AppConstants.maxAllowedDistance,
            fillColor: (isNearby ? AppColors.buttonText : AppColors.buttonText).withOpacity(0.1),
            strokeColor: isNearby ? AppColors.buttonText : AppColors.buttonText,
            strokeWidth: 2,
          ),
        );
      }
    }

    if (mounted) {
      setState(() {});
    }
  }

  double _calculateDistance(double lat1, double lon1, double lat2, double lon2) {
    return Geolocator.distanceBetween(lat1, lon1, lat2, lon2);
  }

  void _selectSite(Site site) {
    setState(() {
      _selectedSite = site;
    });
  }

  void _showSiteDetails(Site site) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => _buildSiteDetailsSheet(site),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('خريطة المواقع', style: TextStyle(color: AppColors.textPrimary)),
        backgroundColor: AppColors.background,
        foregroundColor: AppColors.textPrimary,
        actions: [
          IconButton(
            icon: const Icon(Icons.my_location, color: AppColors.textPrimary),
            onPressed: _getCurrentLocation,
          ),
          IconButton(
            icon: const Icon(Icons.layers, color: AppColors.textPrimary),
            onPressed: _showMapTypeSelector,
          ),
        ],
      ),
      body: Stack(
        children: [
          _buildMap(),
          if (_isLoadingLocation) _buildLoadingOverlay(),
          _buildLocationInfo(),
          if (_selectedSite != null) _buildSelectedSiteInfo(),
        ],
      ),
      floatingActionButton: Column(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          FloatingActionButton(
            heroTag: 'zoom_in',
            mini: true,
            onPressed: _zoomIn,
            backgroundColor: AppColors.background,
            child: const Icon(Icons.zoom_in, color: AppColors.textPrimary),
          ),
          const SizedBox(height: 8),
          FloatingActionButton(
            heroTag: 'zoom_out',
            mini: true,
            onPressed: _zoomOut,
            backgroundColor: AppColors.background,
            child: const Icon(Icons.zoom_out, color: AppColors.textPrimary),
          ),
          const SizedBox(height: 8),
          FloatingActionButton(
            heroTag: 'center_location',
            onPressed: _centerOnCurrentLocation,
            backgroundColor: AppColors.background,
            child: const Icon(Icons.gps_fixed, color: AppColors.textPrimary),
          ),
        ],
      ),
    );
  }

  Widget _buildMap() {
    return GoogleMap(
      onMapCreated: (GoogleMapController controller) {
        _mapController = controller;
        if (_currentPosition != null) {
          _updateMapLocation();
        }
      },
      initialCameraPosition: CameraPosition(
        target: _currentPosition != null
            ? LatLng(_currentPosition!.latitude, _currentPosition!.longitude)
            : const LatLng(24.7136, 46.6753), // Riyadh coordinates
        zoom: AppConstants.defaultZoom,
      ),
      markers: _markers,
      circles: _circles,
      myLocationEnabled: false, // We handle this manually
      myLocationButtonEnabled: false,
      zoomControlsEnabled: false,
      mapToolbarEnabled: false,
      onTap: (LatLng position) {
        setState(() {
          _selectedSite = null;
        });
      },
    );
  }

  Widget _buildLoadingOverlay() {
    return Container(
      color: AppColors.background.withOpacity(0.3),
      child: const Center(
        child: Card(
          color: AppColors.background,
          child: Padding(
            padding: EdgeInsets.all(AppConstants.defaultPadding),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                CircularProgressIndicator(color: AppColors.textPrimary),
                SizedBox(height: AppConstants.defaultPadding),
                Text('جاري تحديد الموقع...', style: TextStyle(color: AppColors.textPrimary)),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildLocationInfo() {
    return Positioned(
      top: 0,
      left: 0,
      right: 0,
      child: Container(
        margin: const EdgeInsets.all(AppConstants.defaultPadding),
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        decoration: BoxDecoration(
          color: AppColors.background,
          borderRadius: BorderRadius.circular(AppConstants.borderRadius),
          boxShadow: [
            BoxShadow(
              color: AppColors.background.withOpacity(0.2),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  _currentPosition != null ? Icons.location_on : Icons.location_off,
                  color: _currentPosition != null ? AppColors.buttonText : AppColors.buttonText,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    _currentPosition != null
                        ? 'الموقع الحالي محدد'
                        : 'لا يمكن تحديد الموقع',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: _currentPosition != null ? AppColors.buttonText : AppColors.buttonText,
                    ),
                  ),
                ),
              ],
            ),
            if (_currentPosition != null) ...[
              const SizedBox(height: 8),
              Text(
                'خط العرض: ${_currentPosition!.latitude.toStringAsFixed(6)}',
                style: TextStyle(color: AppColors.textPrimary),
              ),
              Text(
                'خط الطول: ${_currentPosition!.longitude.toStringAsFixed(6)}',
                style: TextStyle(color: AppColors.textPrimary),
              ),
              Text(
                'الدقة: ${_currentPosition!.accuracy.toStringAsFixed(1)} متر',
                style: TextStyle(color: AppColors.textPrimary),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildSelectedSiteInfo() {
    if (_selectedSite == null) return const SizedBox.shrink();

    final distance = _currentPosition != null
        ? _calculateDistance(
            _currentPosition!.latitude,
            _currentPosition!.longitude,
            _selectedSite!.latitude,
            _selectedSite!.longitude,
          )
        : null;

    final isInRange = distance != null && distance <= AppConstants.maxAllowedDistance;

    return Positioned(
      bottom: 0,
      left: 0,
      right: 0,
      child: Container(
        margin: const EdgeInsets.all(AppConstants.defaultPadding),
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        decoration: BoxDecoration(
          color: AppColors.background,
          borderRadius: BorderRadius.circular(AppConstants.borderRadius),
          boxShadow: [
            BoxShadow(
              color: AppColors.background.withOpacity(0.2),
              blurRadius: 10,
              offset: const Offset(0, -2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: isInRange ? AppColors.buttonText.withOpacity(0.1) : AppColors.buttonText.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.location_on,
                    color: isInRange ? AppColors.buttonText : AppColors.buttonText,
                  ),
                ),
                const SizedBox(width: AppConstants.defaultPadding),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        _selectedSite!.name,
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: AppColors.textPrimary,
                        ),
                      ),
                      if (distance != null)
                        Text(
                          'المسافة: ${distance.toStringAsFixed(1)} متر',
                          style: TextStyle(
                            color: AppColors.textPrimary,
                          ),
                        ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: isInRange ? AppColors.buttonText.withOpacity(0.1) : AppColors.buttonText.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: isInRange ? AppColors.buttonText.withOpacity(0.3) : AppColors.buttonText.withOpacity(0.3),
                    ),
                  ),
                  child: Text(
                    isInRange ? 'في النطاق' : 'خارج النطاق',
                    style: TextStyle(
                      color: isInRange ? AppColors.buttonText : AppColors.buttonText,
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            Row(
              children: [
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () => _navigateToSite(_selectedSite!),
                    icon: const Icon(Icons.directions, color: AppColors.buttonText),
                    label: const Text('الاتجاهات', style: TextStyle(color: AppColors.buttonText)),
                    style: OutlinedButton.styleFrom(
                      side: BorderSide(color: AppColors.buttonText),
                      backgroundColor: AppColors.buttonBackground,
                    ),
                  ),
                ),
                const SizedBox(width: AppConstants.defaultPadding),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _showSiteDetails(_selectedSite!),
                    icon: const Icon(Icons.info, color: AppColors.textPrimary),
                    label: const Text('التفاصيل', style: TextStyle(color: AppColors.textPrimary)),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.buttonBackground,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSiteDetailsSheet(Site site) {
    final distance = _currentPosition != null
        ? _calculateDistance(
            _currentPosition!.latitude,
            _currentPosition!.longitude,
            site.latitude,
            site.longitude,
          )
        : null;

    return Container(
      decoration: BoxDecoration(
        color: AppColors.background,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Center(
              child: Container(
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: AppColors.textPrimary,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            Text(
              site.name,
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: AppColors.textPrimary,
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            _buildDetailRow('رقم الموقع', site.id.toString()),
            _buildDetailRow('خط العرض', site.latitude.toStringAsFixed(6)),
            _buildDetailRow('خط الطول', site.longitude.toStringAsFixed(6)),
            if (distance != null)
              _buildDetailRow('المسافة من موقعك', '${distance.toStringAsFixed(1)} متر'),
            const SizedBox(height: AppConstants.defaultPadding),
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: () => Navigator.pop(context),
                    child: const Text('إغلاق', style: TextStyle(color: AppColors.buttonText)),
                    style: OutlinedButton.styleFrom(
                      side: BorderSide(color: AppColors.buttonText),
                      backgroundColor: AppColors.buttonBackground,
                    ),
                  ),
                ),
                const SizedBox(width: AppConstants.defaultPadding),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () {
                      Navigator.pop(context);
                      _centerOnSite(site);
                    },
                    child: const Text('التركيز على الموقع', style: TextStyle(color: AppColors.textPrimary)),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.buttonBackground,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          SizedBox(
            width: 100,
            child: Text(
              label,
              style: TextStyle(
                color: AppColors.textPrimary,
              ),
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontWeight: FontWeight.w600,
              color: AppColors.textPrimary,
            ),
          ),
        ],
      ),
    );
  }

  void _zoomIn() {
    _mapController?.animateCamera(CameraUpdate.zoomIn());
  }

  void _zoomOut() {
    _mapController?.animateCamera(CameraUpdate.zoomOut());
  }

  void _centerOnCurrentLocation() {
    if (_currentPosition != null && _mapController != null) {
      _mapController!.animateCamera(
        CameraUpdate.newLatLngZoom(
          LatLng(_currentPosition!.latitude, _currentPosition!.longitude),
          AppConstants.defaultZoom,
        ),
      );
    } else {
      _getCurrentLocation();
    }
  }

  void _centerOnSite(Site site) {
    if (_mapController != null) {
      _mapController!.animateCamera(
        CameraUpdate.newLatLngZoom(
          LatLng(site.latitude, site.longitude),
          AppConstants.defaultZoom,
        ),
      );
    }
  }

  void _navigateToSite(Site site) {
    // TODO: Implement navigation to external maps app
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('سيتم فتح تطبيق الخرائط قريباً', style: TextStyle(color: AppColors.textPrimary))),
    );
  }

  void _showMapTypeSelector() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        color: AppColors.background,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.map, color: AppColors.textPrimary),
              title: const Text('خريطة عادية', style: TextStyle(color: AppColors.textPrimary)),
              onTap: () {
                // TODO: Change map type
                Navigator.pop(context);
              },
            ),
            ListTile(
              leading: const Icon(Icons.satellite, color: AppColors.textPrimary),
              title: const Text('خريطة الأقمار الصناعية', style: TextStyle(color: AppColors.textPrimary)),
              onTap: () {
                // TODO: Change map type
                Navigator.pop(context);
              },
            ),
            ListTile(
              leading: const Icon(Icons.terrain, color: AppColors.textPrimary),
              title: const Text('خريطة التضاريس', style: TextStyle(color: AppColors.textPrimary)),
              onTap: () {
                // TODO: Change map type
                Navigator.pop(context);
              },
            ),
          ],
        ),
      ),
    );
  }
}