import 'dart:io';
import 'package:flutter/foundation.dart';
import '../models/models.dart';
import '../constants/app_constants.dart';
import 'http_service.dart';
import 'api_service.dart';
import 'file_manager_service.dart' as fm;
import 'android_download_service.dart' as ads;
import 'excel_generator_service.dart';
class ReportsService {
  static final ReportsService _instance = ReportsService._internal();
  factory ReportsService() => _instance;
  ReportsService._internal();

  final HttpService _httpService = HttpService();
  final fm.FileManagerService _fileManagerService = fm.FileManagerService();

  /// Generate employee report for all employees
  Future<ReportResponse> generateEmployeeReport({
    required String startDate,
    required String endDate,
    bool includeStats = true,
  }) async {
    try {
      debugPrint('ReportsService: Generating employee report from $startDate to $endDate');
      
      final data = {
        'start_date': startDate,
        'end_date': endDate,
        'include_stats': includeStats,
      };

      final response = await _httpService.post<ReportResponse>(
        '${AppConstants.reportsEndpoint}/employees',
        data: data,
        fromJson: (json) => ReportResponse.fromJson(json),
      );

      if (response.success && response.data != null) {
        debugPrint('ReportsService: Employee report generated successfully');
        return response.data!;
      } else {
        throw Exception(response.displayMessage);
      }
    } catch (e) {
      debugPrint('ReportsService: Error generating employee report: $e');
      rethrow;
    }
  }

  /// Generate individual employee report
  Future<ReportResponse> generateIndividualReport({
    required int userId,
    required String startDate,
    required String endDate,
  }) async {
    try {
      debugPrint('ReportsService: Generating individual report for user $userId');
      
      final data = {
        'start_date': startDate,
        'end_date': endDate,
      };

      final response = await _httpService.post<ReportResponse>(
        '${AppConstants.reportsEndpoint}/employees/$userId',
        data: data,
        fromJson: (json) => ReportResponse.fromJson(json),
      );

      if (response.success && response.data != null) {
        debugPrint('ReportsService: Individual report generated successfully');
        return response.data!;
      } else {
        throw Exception(response.displayMessage);
      }
    } catch (e) {
      debugPrint('ReportsService: Error generating individual report: $e');
      rethrow;
    }
  }

  /// Generate site report
  Future<ReportResponse> generateSiteReport({
    required int siteId,
    required String startDate,
    required String endDate,
  }) async {
    try {
      debugPrint('ReportsService: Generating site report for site $siteId');
      
      final data = {
        'start_date': startDate,
        'end_date': endDate,
      };

      final response = await _httpService.post<ReportResponse>(
        '${AppConstants.reportsEndpoint}/sites/$siteId',
        data: data,
        fromJson: (json) => ReportResponse.fromJson(json),
      );

      if (response.success && response.data != null) {
        debugPrint('ReportsService: Site report generated successfully');
        return response.data!;
      } else {
        throw Exception(response.displayMessage);
      }
    } catch (e) {
      debugPrint('ReportsService: Error generating site report: $e');
      rethrow;
    }
  }

  /// Verify employee presence
  Future<PresenceVerificationResponse> verifyEmployeePresence({
    required int userId,
    required double latitude,
    required double longitude,
    bool sendAlert = false,
  }) async {
    try {
      debugPrint('ReportsService: Verifying presence for user $userId');
      
      final data = {
        'user_id': userId,
        'latitude': latitude,
        'longitude': longitude,
        'send_alert': sendAlert,
      };

      final response = await _httpService.post<PresenceVerificationResponse>(
        '${AppConstants.reportsEndpoint}/verify-presence',
        data: data,
        fromJson: (json) => PresenceVerificationResponse.fromJson(json),
      );

      if (response.success && response.data != null) {
        debugPrint('ReportsService: Presence verification completed');
        return response.data!;
      } else {
        throw Exception(response.displayMessage);
      }
    } catch (e) {
      debugPrint('ReportsService: Error verifying presence: $e');
      rethrow;
    }
  }

  /// Check all active employees
  Future<AllEmployeesCheckResponse> checkAllActiveEmployees() async {
    try {
      debugPrint('ReportsService: Checking all active employees');

      final apiService = ApiService();
      final response = await apiService.checkAllEmployeesPresence();

      debugPrint('ReportsService: All employees check completed');
      return response;
    } catch (e) {
      debugPrint('ReportsService: Error checking all employees: $e');
      rethrow;
    }
  }

  /// Download report file and return content or save Excel file
  Future<DownloadResult> downloadReport(String filename) async {
    try {
      debugPrint('ReportsService: Downloading report $filename');

      // Detect file type by extension
      final isExcelFile = filename.toLowerCase().endsWith('.xlsx');

      if (isExcelFile) {
        // For Excel files, download as binary and save
        final bytes = await _httpService.downloadBinaryFile(
          '${AppConstants.reportsEndpoint}/download/$filename',
        );

        debugPrint('ReportsService: Excel file downloaded successfully, size: ${bytes.length} bytes');

        // Use Android service to save and open
        String? filePath;
        if (Platform.isAndroid) {
          debugPrint('ReportsService: Using Android service to save and open Excel file');
          filePath = await ads.AndroidDownloadService.saveAndOpenExcelFile(filename, Uint8List.fromList(bytes));
        } else {
          // Fallback to generic service for iOS
          debugPrint('ReportsService: Using generic file manager service');
          filePath = await _fileManagerService.saveExcelFile(filename, bytes);
        }

        if (filePath != null) {
          return DownloadResult(
            success: true,
            isExcelFile: true,
            filePath: filePath,
            content: 'Fichier Excel sauvegardé et ouvert: $filename (${ads.AndroidDownloadService.formatFileSize(bytes.length)})',
            filename: filename,
          );
        } else {
          throw Exception('Impossible de sauvegarder le fichier Excel dans le dossier Téléchargements');
        }
      } else {
        // For CSV files, return content
        final content = await _httpService.getFileContent(
          '${AppConstants.reportsEndpoint}/download/$filename',
        );

        debugPrint('ReportsService: CSV file downloaded successfully, content length: ${content.length}');

        // Save CSV file locally
        String? filePath;
        if (Platform.isAndroid) {
          filePath = await ads.AndroidDownloadService.saveCsvFile(filename, content);
        } else {
          filePath = await _fileManagerService.saveExcelFile(filename, Uint8List.fromList(content.codeUnits));
        }

        if (filePath != null) {
          return DownloadResult(
            success: true,
            isExcelFile: false,
            filePath: filePath,
            content: content,
            filename: filename,
          );
        } else {
          throw Exception('Impossible de sauvegarder le fichier CSV');
        }
      }
    } catch (e) {
      debugPrint('ReportsService: Error downloading report: $e');
      return DownloadResult(
        success: false,
        isExcelFile: filename.toLowerCase().endsWith('.xlsx'),
        content: 'Erreur: ${e.toString()}',
        filename: filename,
      );
    }
  }

  /// Format file size for display
  String formatFileSize(int bytes) {
    const units = ['B', 'KB', 'MB', 'GB'];
    if (bytes <= 0) return '0 B';
    
    final digitGroups = (bytes.bitLength - 1) ~/ 10;
    final index = digitGroups.clamp(0, units.length - 1);
    final size = bytes / (1 << (index * 10));
    
    return '${size.toStringAsFixed(2)} ${units[index]}';
  }

  /// Get report type from filename
  String getReportType(String filename) {
    if (filename.contains('employes')) {
      return 'تقرير الموظفين';
    } else if (filename.contains('site')) {
      return 'تقرير الموقع';
    } else if (filename.contains('individual')) {
      return 'تقرير فردي';
    }
    return 'تقرير';
  }

  /// Check if report is recent (generated within last 24 hours)
  bool isRecentReport(String generatedAt) {
    try {
      final generated = DateTime.parse(generatedAt);
      final now = DateTime.now();
      final difference = now.difference(generated);
      return difference.inHours < 24;
    } catch (e) {
      return false;
    }
  }

  /// Open a downloaded Excel file
  Future<bool> openExcelFile(String filePath) async {
    if (Platform.isAndroid) {
      return await ads.AndroidDownloadService.openExcelFile(filePath);
    } else {
      return await _fileManagerService.openExcelFile(filePath);
    }
  }

  /// Share a downloaded Excel file
  Future<bool> shareExcelFile(String filePath, String filename) async {
    if (Platform.isAndroid) {
      return await ads.AndroidDownloadService.shareExcelFile(filePath, filename);
    } else {
      return await _fileManagerService.shareExcelFile(filePath, filename);
    }
  }

  /// Share via WhatsApp specifically
  Future<bool> shareViaWhatsApp(String filePath, String filename) async {
    if (Platform.isAndroid) {
      return await ads.AndroidDownloadService.shareViaWhatsApp(filePath, filename);
    } else {
      return await _fileManagerService.shareExcelFile(filePath, filename);
    }
  }

  /// Share via Email specifically
  Future<bool> shareViaEmail(String filePath, String filename) async {
    if (Platform.isAndroid) {
      return await ads.AndroidDownloadService.shareViaEmail(filePath, filename);
    } else {
      return await _fileManagerService.shareExcelFile(filePath, filename);
    }
  }

  /// Get list of saved reports
  Future<List<FileInfo>> getSavedReports() async {
    if (Platform.isAndroid) {
      return await ads.AndroidDownloadService.getDownloadedReports();
    } else {
      // Convert FileInfo from generic service to match Android type
      final reports = await _fileManagerService.getSavedReports();
      return reports.map((report) => FileInfo(
        name: report.name,
        path: report.path,
        size: report.size,
        modifiedDate: report.modifiedDate,
      )).toList();
    }
  }

  /// Delete a saved report
  Future<bool> deleteReport(String filePath) async {
    if (Platform.isAndroid) {
      return await ads.AndroidDownloadService.deleteReport(filePath);
    } else {
      return await _fileManagerService.deleteReport(filePath);
    }
  }

  /// Generate and download a CSV report with real database data
  Future<DownloadResult?> generateAndDownloadCSVReport({
    required String reportType,
    required DateTime startDate,
    required DateTime endDate,
    int? employeeId,
    int? siteId,
  }) async {
    try {
      debugPrint('ReportsService: Generating CSV report with real database data');
      debugPrint('ReportsService: Report type: $reportType');
      debugPrint('ReportsService: Date range: ${startDate.toIso8601String()} to ${endDate.toIso8601String()}');

      // 1. Fetch data from database via API
      List<Map<String, dynamic>> attendanceData = [];

      switch (reportType) {
        case 'all_employees':
          attendanceData = await _fetchAllEmployeesAttendance(startDate, endDate);
          break;
        case 'individual':
          if (employeeId != null) {
            attendanceData = await _fetchEmployeeAttendance(employeeId, startDate, endDate);
          }
          break;
        case 'site':
          if (siteId != null) {
            attendanceData = await _fetchSiteAttendance(siteId, startDate, endDate);
          }
          break;
      }

      if (attendanceData.isEmpty) {
        debugPrint('ReportsService: No attendance data found for the specified criteria');
        debugPrint('ReportsService: Report type: $reportType, Employee ID: $employeeId, Site ID: $siteId');
        return DownloadResult(
          success: false,
          isExcelFile: false,
          content: 'لا توجد بيانات حضور للفترة المحددة. تأكد من وجود بيانات حضور في هذه الفترة.',
          filename: '',
        );
      }

      debugPrint('ReportsService: Found ${attendanceData.length} attendance records');

      // 2. Generate CSV file with real data
      final csvBytes = await ExcelGeneratorService.generateExcelReport(
        reportType: reportType,
        startDate: startDate,
        endDate: endDate,
        employeeData: attendanceData,
        selectedEmployeeId: employeeId,
        selectedSiteId: siteId,
      );

      // 3. Generate filename
      final filename = ExcelGeneratorService.generateFileName(reportType);

      // 4. Save file locally
      String? filePath;
      if (Platform.isAndroid) {
        // Convert to Uint8List if csvBytes is List<int>
        final bytes = csvBytes is Uint8List ? csvBytes : Uint8List.fromList(csvBytes);
        filePath = await ads.AndroidDownloadService.saveAndOpenExcelFile(filename, bytes);
      } else {
        filePath = await _fileManagerService.saveExcelFile(filename, csvBytes);
      }

      if (filePath != null) {
        debugPrint('ReportsService: CSV report generated and saved successfully');
        return DownloadResult(
          success: true,
          isExcelFile: false,
          filePath: filePath,
          content: 'Rapport CSV généré avec ${attendanceData.length} enregistrements',
          filename: filename,
        );
      } else {
        throw Exception('Impossible de sauvegarder le fichier CSV');
      }
    } catch (e) {
      debugPrint('ReportsService: Error generating CSV report: $e');
      return DownloadResult(
        success: false,
        isExcelFile: false,
        content: 'Erreur lors de la génération du rapport: $e',
        filename: '',
      );
    }
  }

  /// Fetch attendance data for all employees
  Future<List<Map<String, dynamic>>> _fetchAllEmployeesAttendance(DateTime startDate, DateTime endDate) async {
    try {
      debugPrint('ReportsService: Fetching all employees attendance from ${startDate.toIso8601String().split('T')[0]} to ${endDate.toIso8601String().split('T')[0]}');

      // Try new endpoint first
      try {
        final response = await _httpService.get(
          '${AppConstants.reportsEndpoint}/attendance',
          queryParameters: {
            'start_date': startDate.toIso8601String().split('T')[0],
            'end_date': endDate.toIso8601String().split('T')[0],
            'type': 'all_employees',
          },
        );

        debugPrint('ReportsService: New endpoint response - success: ${response.success}');

        if (response.success && response.data != null) {
          final List<dynamic> rawData = response.data is Map
              ? (response.data['attendance'] ?? [])
              : (response.data is List ? response.data : []);

          debugPrint('ReportsService: Raw data count from new endpoint: ${rawData.length}');

          final convertedData = rawData.map((item) => _convertAttendanceData(item)).toList();
          debugPrint('ReportsService: Converted data count: ${convertedData.length}');

          return convertedData;
        }
      } catch (e) {
        debugPrint('ReportsService: New endpoint not available, trying fallback: $e');
      }

      // Fallback: use existing pointage endpoint
      debugPrint('ReportsService: Using fallback pointage endpoint');
      final response = await _httpService.get(
        AppConstants.pointageEndpoint,
        queryParameters: {
          'start_date': startDate.toIso8601String().split('T')[0],
          'end_date': endDate.toIso8601String().split('T')[0],
        },
      );

      debugPrint('ReportsService: Fallback response - success: ${response.success}');

      if (response.success && response.data != null) {
        List<dynamic> rawData = [];

        // Handle different response formats
        if (response.data is List) {
          rawData = response.data;
        } else if (response.data is Map) {
          final dataMap = response.data as Map<String, dynamic>;
          if (dataMap.containsKey('data') && dataMap['data'] is List) {
            rawData = dataMap['data'];
          } else if (dataMap.containsKey('pointages') && dataMap['pointages'] is List) {
            rawData = dataMap['pointages'];
          } else {
            // If it's a single object, put it in a list
            rawData = [dataMap];
          }
        }

        debugPrint('ReportsService: Fallback raw data count: ${rawData.length}');
        debugPrint('ReportsService: First item sample: ${rawData.isNotEmpty ? rawData.first : 'No data'}');

        final convertedData = rawData.map((item) => _convertPointageData(item)).toList();
        debugPrint('ReportsService: Fallback converted data count: ${convertedData.length}');

        return convertedData;
      }

      debugPrint('ReportsService: No data received from any endpoint');
      return [];
    } catch (e) {
      debugPrint('ReportsService: Error fetching all employees attendance: $e');
      return [];
    }
  }

  /// Fetch attendance data for a specific employee
  Future<List<Map<String, dynamic>>> _fetchEmployeeAttendance(int employeeId, DateTime startDate, DateTime endDate) async {
    try {
      debugPrint('ReportsService: Fetching employee $employeeId attendance from ${startDate.toIso8601String().split('T')[0]} to ${endDate.toIso8601String().split('T')[0]}');

      // Try new endpoint first
      try {
        final response = await _httpService.get(
          '${AppConstants.reportsEndpoint}/attendance',
          queryParameters: {
            'start_date': startDate.toIso8601String().split('T')[0],
            'end_date': endDate.toIso8601String().split('T')[0],
            'type': 'individual',
            'employee_id': employeeId.toString(),
          },
        );

        debugPrint('ReportsService: Employee attendance response - success: ${response.success}');

        if (response.success && response.data != null) {
          final List<dynamic> rawData = response.data is Map
              ? (response.data['attendance'] ?? [])
              : (response.data is List ? response.data : []);

          debugPrint('ReportsService: Employee attendance raw data count: ${rawData.length}');

          final convertedData = rawData.map((item) => _convertAttendanceData(item)).toList();
          debugPrint('ReportsService: Employee attendance converted data count: ${convertedData.length}');

          return convertedData;
        }
      } catch (e) {
        debugPrint('ReportsService: New endpoint not available for employee, trying fallback: $e');
      }

      // Fallback: use existing pointage endpoint with user filter
      debugPrint('ReportsService: Using fallback pointage endpoint for employee $employeeId');
      final response = await _httpService.get(
        AppConstants.pointageEndpoint,
        queryParameters: {
          'user_id': employeeId.toString(),
          'start_date': startDate.toIso8601String().split('T')[0],
          'end_date': endDate.toIso8601String().split('T')[0],
        },
      );

      debugPrint('ReportsService: Employee fallback response - success: ${response.success}');
      debugPrint('ReportsService: Employee fallback response.data type: ${response.data.runtimeType}');
      debugPrint('ReportsService: Employee fallback response.data: ${response.data}');

      if (response.success && response.data != null) {
        List<dynamic> rawData = [];

        // Handle different response formats
        if (response.data is List) {
          rawData = response.data;
          debugPrint('ReportsService: Data is List, count: ${rawData.length}');
        } else if (response.data is Map) {
          final dataMap = response.data as Map<String, dynamic>;
          debugPrint('ReportsService: Data is Map, keys: ${dataMap.keys.toList()}');

          if (dataMap.containsKey('data') && dataMap['data'] is List) {
            rawData = dataMap['data'];
            debugPrint('ReportsService: Found data array, count: ${rawData.length}');
          } else if (dataMap.containsKey('pointages') && dataMap['pointages'] is List) {
            rawData = dataMap['pointages'];
            debugPrint('ReportsService: Found pointages array, count: ${rawData.length}');
          } else {
            // If it's a single object, put it in a list
            rawData = [dataMap];
            debugPrint('ReportsService: Treating single object as array, count: 1');
          }
        } else {
          debugPrint('ReportsService: Unexpected data type: ${response.data.runtimeType}');
        }

        debugPrint('ReportsService: Employee fallback raw data count: ${rawData.length}');
        debugPrint('ReportsService: First item sample: ${rawData.isNotEmpty ? rawData.first : 'No data'}');

        if (rawData.isNotEmpty) {
          final convertedData = rawData.map((item) => _convertPointageData(item)).toList();
          debugPrint('ReportsService: Employee fallback converted data count: ${convertedData.length}');

          // Verify conversion success
          final validData = convertedData.where((item) =>
            item['name'] != 'غير محدد' ||
            item['checkIn'] != '00:00' ||
            item['checkOut'] != '00:00'
          ).toList();

          debugPrint('ReportsService: Valid converted data count: ${validData.length}');

          return convertedData;
        } else {
          debugPrint('ReportsService: Raw data is empty after extraction');
        }
      } else {
        debugPrint('ReportsService: Response not successful or data is null');
        debugPrint('ReportsService: response.success = ${response.success}');
        debugPrint('ReportsService: response.data = ${response.data}');
      }

      debugPrint('ReportsService: No employee attendance data received from any endpoint');
      return [];
    } catch (e) {
      debugPrint('ReportsService: Error fetching employee attendance: $e');
      return [];
    }
  }

  /// Fetch attendance data for a specific site
  Future<List<Map<String, dynamic>>> _fetchSiteAttendance(int siteId, DateTime startDate, DateTime endDate) async {
    try {
      debugPrint('ReportsService: Fetching site $siteId attendance from ${startDate.toIso8601String().split('T')[0]} to ${endDate.toIso8601String().split('T')[0]}');

      // Try new endpoint first
      try {
        final response = await _httpService.get(
          '${AppConstants.reportsEndpoint}/attendance',
          queryParameters: {
            'start_date': startDate.toIso8601String().split('T')[0],
            'end_date': endDate.toIso8601String().split('T')[0],
            'type': 'site',
            'site_id': siteId.toString(),
          },
        );

        debugPrint('ReportsService: Site attendance response - success: ${response.success}');

        if (response.success && response.data != null) {
          final List<dynamic> rawData = response.data is Map
              ? (response.data['attendance'] ?? [])
              : (response.data is List ? response.data : []);

          debugPrint('ReportsService: Site attendance raw data count: ${rawData.length}');

          final convertedData = rawData.map((item) => _convertAttendanceData(item)).toList();
          debugPrint('ReportsService: Site attendance converted data count: ${convertedData.length}');

          return convertedData;
        }
      } catch (e) {
        debugPrint('ReportsService: New endpoint not available for site, trying fallback: $e');
      }

      // Fallback: use existing pointage endpoint with site filter
      debugPrint('ReportsService: Using fallback pointage endpoint for site $siteId');
      final response = await _httpService.get(
        AppConstants.pointageEndpoint,
        queryParameters: {
          'site_id': siteId.toString(),
          'start_date': startDate.toIso8601String().split('T')[0],
          'end_date': endDate.toIso8601String().split('T')[0],
        },
      );

      debugPrint('ReportsService: Site fallback response - success: ${response.success}');

      if (response.success && response.data != null) {
        List<dynamic> rawData = [];

        // Handle different response formats
        if (response.data is List) {
          rawData = response.data;
        } else if (response.data is Map) {
          final dataMap = response.data as Map<String, dynamic>;
          if (dataMap.containsKey('data') && dataMap['data'] is List) {
            rawData = dataMap['data'];
          } else if (dataMap.containsKey('pointages') && dataMap['pointages'] is List) {
            rawData = dataMap['pointages'];
          } else {
            // If it's a single object, put it in a list
            rawData = [dataMap];
          }
        }

        debugPrint('ReportsService: Site fallback raw data count: ${rawData.length}');
        debugPrint('ReportsService: First item sample: ${rawData.isNotEmpty ? rawData.first : 'No data'}');

        final convertedData = rawData.map((item) => _convertPointageData(item)).toList();
        debugPrint('ReportsService: Site fallback converted data count: ${convertedData.length}');

        return convertedData;
      }

      debugPrint('ReportsService: No site attendance data received from any endpoint');
      return [];
    } catch (e) {
      debugPrint('ReportsService: Error fetching site attendance: $e');
      return [];
    }
  }

  /// Convert attendance data to expected format for Excel generator
  Map<String, dynamic> _convertAttendanceData(dynamic item) {
    debugPrint('ReportsService: Converting attendance data: $item');

    if (item is Map<String, dynamic>) {
      final checkIn = item['check_in'] ?? item['time_in'] ?? '00:00';
      final checkOut = item['check_out'] ?? item['time_out'] ?? '00:00';

      final convertedData = {
        'id': item['id'] ?? item['employee_id'] ?? 0,
        'name': item['employee_name'] ?? item['name'] ?? 'غير محدد',
        'site': item['site_name'] ?? item['site'] ?? 'غير محدد',
        'date': item['date'] ?? DateTime.now().toIso8601String().split('T')[0],
        'checkIn': checkIn,
        'checkOut': checkOut,
        'totalHours': item['total_hours'] ?? _calculateTotalHours(checkIn, checkOut),
        'status': item['status'] ?? 'unknown',
      };

      debugPrint('ReportsService: Converted data: $convertedData');
      return convertedData;
    }

    // Fallback for malformed data
    debugPrint('ReportsService: Using fallback data for invalid item: $item');
    return {
      'id': 0,
      'name': 'غير محدد',
      'site': 'غير محدد',
      'date': DateTime.now().toIso8601String().split('T')[0],
      'checkIn': '00:00',
      'checkOut': '00:00',
      'totalHours': '0 ساعات',
      'status': 'unknown',
    };
  }

  /// Convert pointage data (existing backend format) to expected format
  Map<String, dynamic> _convertPointageData(dynamic item) {
    debugPrint('ReportsService: Converting pointage data type: ${item.runtimeType}');
    debugPrint('ReportsService: Converting pointage data: $item');

    if (item is Map<String, dynamic>) {
      // Extract pointage information
      final debutPointage = item['debut_pointage'];
      final finPointage = item['fin_pointage'];
      final duree = item['duree'];
      final userId = item['user'];
      final siteData = item['site'];

      debugPrint('ReportsService: Raw fields - debut: $debutPointage, fin: $finPointage, duree: $duree');
      debugPrint('ReportsService: User data: $userId');
      debugPrint('ReportsService: Site data: $siteData');

      String checkIn = '00:00';
      String checkOut = '00:00';
      String date = DateTime.now().toIso8601String().split('T')[0];
      String totalHours = '0 ساعات';
      String userName = 'غير محدد';
      String siteName = 'غير محدد';

      // Extract user name
      if (userId is Map<String, dynamic> && userId['name'] != null) {
        userName = userId['name'].toString();
        debugPrint('ReportsService: Extracted user name: $userName');
      }

      // Extract site name
      if (siteData is Map<String, dynamic> && siteData['name'] != null) {
        siteName = siteData['name'].toString();
        debugPrint('ReportsService: Extracted site name: $siteName');
      }

      if (debutPointage != null) {
        try {
          // Handle format "2025-08-03 00:13:29"
          final debutDateTime = DateTime.parse(debutPointage.toString().replaceAll(' ', 'T'));
          checkIn = '${debutDateTime.hour.toString().padLeft(2, '0')}:${debutDateTime.minute.toString().padLeft(2, '0')}';
          date = debutDateTime.toIso8601String().split('T')[0];
          debugPrint('ReportsService: Parsed debut_pointage: $checkIn on $date');
        } catch (e) {
          debugPrint('ReportsService: Error parsing debut_pointage: $e');
        }
      }

      if (finPointage != null) {
        try {
          // Handle format "2025-08-03 00:13:54"
          final finDateTime = DateTime.parse(finPointage.toString().replaceAll(' ', 'T'));
          checkOut = '${finDateTime.hour.toString().padLeft(2, '0')}:${finDateTime.minute.toString().padLeft(2, '0')}';
          debugPrint('ReportsService: Parsed fin_pointage: $checkOut');
        } catch (e) {
          debugPrint('ReportsService: Error parsing fin_pointage: $e');
        }
      }

      // Use backend duration if available, otherwise calculate
      if (duree != null && duree.toString().isNotEmpty) {
        totalHours = _formatDuration(duree.toString());
        debugPrint('ReportsService: Using backend duration: $duree -> $totalHours');
      } else {
        totalHours = _calculateTotalHours(checkIn, checkOut);
        debugPrint('ReportsService: Calculated duration: $totalHours');
      }

      final convertedData = {
        'id': item['id'] ?? item['user_id'] ?? 0,
        'name': userName,
        'site': siteName,
        'date': date,
        'checkIn': checkIn,
        'checkOut': checkOut,
        'totalHours': totalHours,
        'status': finPointage != null ? 'completed' : 'active',
      };

      debugPrint('ReportsService: Final converted pointage data: $convertedData');
      debugPrint('ReportsService: Conversion successful for item ID: ${item['id']}');
      return convertedData;
    }

    // Fallback for malformed data
    debugPrint('ReportsService: Using fallback data for invalid pointage item: $item');
    return {
      'id': 0,
      'name': 'غير محدد',
      'site': 'غير محدد',
      'date': DateTime.now().toIso8601String().split('T')[0],
      'checkIn': '00:00',
      'checkOut': '00:00',
      'totalHours': '0 ساعات',
      'status': 'unknown',
    };
  }

  /// Calculate total hours between check-in and check-out
  String _calculateTotalHours(String checkIn, String checkOut) {
    try {
      if (checkIn.isEmpty || checkOut.isEmpty || checkIn == '00:00' || checkOut == '00:00') {
        return '0 ساعات';
      }

      final inParts = checkIn.split(':');
      final outParts = checkOut.split(':');

      final inMinutes = int.parse(inParts[0]) * 60 + int.parse(inParts[1]);
      final outMinutes = int.parse(outParts[0]) * 60 + int.parse(outParts[1]);

      final totalMinutes = outMinutes - inMinutes;

      if (totalMinutes <= 0) {
        return '0 ساعات';
      }

      final hours = totalMinutes ~/ 60;
      final minutes = totalMinutes % 60;

      if (minutes == 0) {
        return '$hours ساعات';
      } else {
        return '$hours ساعات و $minutes دقيقة';
      }
    } catch (e) {
      return '0 ساعات';
    }
  }

  /// Format backend duration (format HH:MM:SS) to Arabic text
  String _formatDuration(String duration) {
    try {
      // Expected format: "00:00:25" or "01:30:45"
      final parts = duration.split(':');
      if (parts.length >= 3) {
        final hours = int.parse(parts[0]);
        final minutes = int.parse(parts[1]);
        final seconds = int.parse(parts[2]);

        if (hours > 0) {
          if (minutes > 0) {
            return '$hours ساعات و $minutes دقيقة';
          } else {
            return '$hours ساعات';
          }
        } else if (minutes > 0) {
          return '$minutes دقيقة';
        } else if (seconds > 0) {
          return '$seconds ثانية';
        }
      }

      return '0 ساعات';
    } catch (e) {
      debugPrint('ReportsService: Error formatting duration: $e');
      return '0 ساعات';
    }
  }
}