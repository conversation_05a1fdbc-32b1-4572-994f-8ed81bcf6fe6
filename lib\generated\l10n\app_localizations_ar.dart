// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Arabic (`ar`).
class AppLocalizationsAr extends AppLocalizations {
  AppLocalizationsAr([String locale = 'ar']) : super(locale);

  @override
  String get appTitle => 'نظام الحضور والانصراف';

  @override
  String get login => 'تسجيل الدخول';

  @override
  String get logout => 'تسجيل الخروج';

  @override
  String get email => 'البريد الإلكتروني';

  @override
  String get password => 'كلمة المرور';

  @override
  String get dashboard => 'لوحة التحكم';

  @override
  String get checkIn => 'تسجيل الحضور';

  @override
  String get checkOut => 'تسجيل الانصراف';

  @override
  String get currentStatus => 'الحالة الحالية';

  @override
  String get assignedSite => 'الموقع المخصص';

  @override
  String get attendanceHistory => 'سجل الحضور';

  @override
  String get employees => 'الموظفون';

  @override
  String get sites => 'المواقع';

  @override
  String get reports => 'التقارير';

  @override
  String get settings => 'الإعدادات';

  @override
  String get loading => 'جاري التحميل...';

  @override
  String get error => 'خطأ';

  @override
  String get success => 'نجح';

  @override
  String get cancel => 'إلغاء';

  @override
  String get confirm => 'تأكيد';

  @override
  String get save => 'حفظ';

  @override
  String get delete => 'حذف';

  @override
  String get edit => 'تعديل';

  @override
  String get add => 'إضافة';

  @override
  String get search => 'بحث';

  @override
  String get noDataFound => 'لا توجد بيانات';

  @override
  String get locationPermissionRequired => 'مطلوب إذن الموقع';

  @override
  String get gpsNotEnabled => 'GPS غير مفعل';

  @override
  String get outOfRange => 'خارج النطاق المسموح';

  @override
  String get checkInSuccess => 'تم تسجيل الحضور بنجاح';

  @override
  String get checkOutSuccess => 'تم تسجيل الانصراف بنجاح';

  @override
  String get invalidCredentials => 'بيانات الدخول غير صحيحة';

  @override
  String get networkError => 'خطأ في الشبكة';

  @override
  String get serverError => 'خطأ في الخادم';

  @override
  String get name => 'الاسم';

  @override
  String get role => 'الدور';

  @override
  String get admin => 'مدير';

  @override
  String get employee => 'موظف';

  @override
  String get latitude => 'خط العرض';

  @override
  String get longitude => 'خط الطول';

  @override
  String get startTime => 'وقت البداية';

  @override
  String get endTime => 'وقت النهاية';

  @override
  String get duration => 'المدة';

  @override
  String get date => 'التاريخ';

  @override
  String get time => 'الوقت';

  @override
  String get today => 'اليوم';

  @override
  String get yesterday => 'أمس';

  @override
  String get thisWeek => 'هذا الأسبوع';

  @override
  String get thisMonth => 'هذا الشهر';

  @override
  String get active => 'نشط';

  @override
  String get inactive => 'غير نشط';

  @override
  String get present => 'حاضر';

  @override
  String get absent => 'غائب';

  @override
  String get workingHours => 'ساعات العمل';

  @override
  String get totalHours => 'إجمالي الساعات';

  @override
  String get overtime => 'ساعات إضافية';

  @override
  String get refresh => 'تحديث';

  @override
  String get retry => 'إعادة المحاولة';

  @override
  String get close => 'إغلاق';

  @override
  String get back => 'رجوع';

  @override
  String get next => 'التالي';

  @override
  String get previous => 'السابق';

  @override
  String get filter => 'تصفية';

  @override
  String get sort => 'ترتيب';

  @override
  String get export => 'تصدير';

  @override
  String get import => 'استيراد';

  @override
  String get profile => 'الملف الشخصي';

  @override
  String get changePassword => 'تغيير كلمة المرور';

  @override
  String get currentPassword => 'كلمة المرور الحالية';

  @override
  String get newPassword => 'كلمة المرور الجديدة';

  @override
  String get confirmPassword => 'تأكيد كلمة المرور';

  @override
  String get passwordChanged => 'تم تغيير كلمة المرور بنجاح';

  @override
  String get fieldRequired => 'هذا الحقل مطلوب';

  @override
  String get invalidEmail => 'البريد الإلكتروني غير صحيح';

  @override
  String get passwordTooShort => 'كلمة المرور قصيرة جداً';

  @override
  String get passwordsDoNotMatch => 'كلمات المرور غير متطابقة';
}
