import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../../../constants/app_colors.dart';
import '../../../constants/app_constants.dart';
import '../../../providers/providers.dart';
import '../../../services/services.dart';

class EmployeeVerificationTab extends StatefulWidget {
  const EmployeeVerificationTab({super.key});

  @override
  State<EmployeeVerificationTab> createState() => _EmployeeVerificationTabState();
}

class _EmployeeVerificationTabState extends State<EmployeeVerificationTab> {
  final LocationService _locationService = LocationService();
  bool _isCheckingLocation = false;
  bool _isVerifyingAll = false;

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildQuickActionsCard(),
          const SizedBox(height: AppConstants.defaultPadding),
          _buildIndividualVerificationCard(),
          const SizedBox(height: AppConstants.defaultPadding),
          _buildLastCheckResults(),
          const SizedBox(height: AppConstants.defaultPadding),
          _buildVerificationHistory(),
        ],
      ),
    );
  }

  Widget _buildQuickActionsCard() {
    return Card(
      elevation: 2,
      color: AppColors.background,
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'إجراءات سريعة',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppColors.buttonText,
              ),
            ),
            const SizedBox(height: AppConstants.smallPadding),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _isVerifyingAll ? null : _checkAllEmployees,
                    icon: _isVerifyingAll
                        ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(strokeWidth: 2, color: AppColors.buttonText),
                          )
                        : const Icon(Icons.group_outlined, color: AppColors.textPrimary),
                    label: Text(
                      _isVerifyingAll ? 'جاري التحقق...' : 'فحص جميع الموظفين',
                      style: const TextStyle(fontWeight: FontWeight.bold, color: AppColors.textPrimary),
                    ),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.buttonBackground,
                      foregroundColor: AppColors.buttonText,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppConstants.smallPadding),
            Text(
              'يقوم هذا الإجراء بفحص حضور جميع الموظفين النشطين في مواقعهم المخصصة',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(color: AppColors.textPrimary),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildIndividualVerificationCard() {
    return Card(
      elevation: 2,
      color: AppColors.background,
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'التحقق من موظف محدد',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppColors.buttonText,
              ),
            ),
            const SizedBox(height: AppConstants.smallPadding),
            Consumer<EmployeesProvider>(
              builder: (context, employeesProvider, child) {
                if (employeesProvider.isLoading) {
                  return const Center(child: CircularProgressIndicator(color: AppColors.buttonText));
                }

                return Column(
                  children: [
                    DropdownButtonFormField<int>(
                      decoration: const InputDecoration(
                        labelText: 'اختر الموظف للتحقق من حضوره',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.person, color: AppColors.textPrimary),
                      ),
                      items: employeesProvider.employees.map((employee) {
                        return DropdownMenuItem<int>(
                          value: employee.id,
                          child: Text(employee.name, style: const TextStyle(color: AppColors.textPrimary)),
                        );
                      }).toList(),
                      onChanged: (employeeId) {
                        if (employeeId != null) {
                          _verifyIndividualEmployee(employeeId);
                        }
                      },
                      dropdownColor: AppColors.background,
                      style: const TextStyle(color: AppColors.textPrimary),
                    ),
                    const SizedBox(height: AppConstants.smallPadding),
                    Row(
                      children: [
                        Checkbox(
                          value: true,
                          onChanged: null,
                          activeColor: AppColors.buttonText,
                          checkColor: AppColors.textPrimary,
                        ),
                        const Expanded(
                          child: Text(
                            'إرسال تنبيه في حالة عدم الحضور',
                            style: TextStyle(fontSize: 14, color: AppColors.textPrimary),
                          ),
                        ),
                      ],
                    ),
                  ],
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLastCheckResults() {
    return Consumer<ReportsProvider>(
      builder: (context, reportsProvider, child) {
        final lastCheck = reportsProvider.lastEmployeeCheck;
        if (lastCheck == null) {
          return const SizedBox.shrink();
        }

        return Card(
          elevation: 2,
          color: AppColors.background,
          child: Padding(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'نتائج آخر فحص شامل',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppColors.buttonText,
                  ),
                ),
                const SizedBox(height: AppConstants.smallPadding),
                Row(
                  children: [
                    Expanded(
                      child: _buildStatCard(
                        'إجمالي المفحوصين',
                        '${lastCheck.totalChecked}',
                        Icons.people,
                        AppColors.buttonText,
                      ),
                    ),
                    const SizedBox(width: AppConstants.smallPadding),
                    Expanded(
                      child: _buildStatCard(
                        'الحاضرين',
                        '${lastCheck.presentCount}',
                        Icons.check_circle,
                        AppColors.buttonText,
                      ),
                    ),
                    const SizedBox(width: AppConstants.smallPadding),
                    Expanded(
                      child: _buildStatCard(
                        'الغائبين',
                        '${lastCheck.absentCount}',
                        Icons.cancel,
                        AppColors.buttonText,
                      ),
                    ),
                  ],
                ),
                if (lastCheck.verifications.isNotEmpty) ...[
                  const SizedBox(height: AppConstants.defaultPadding),
                  const Divider(color: AppColors.textPrimary),
                  const SizedBox(height: AppConstants.smallPadding),
                  Text(
                    'تفاصيل النتائج',
                    style: Theme.of(context).textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: AppColors.textPrimary,
                    ),
                  ),
                  const SizedBox(height: AppConstants.smallPadding),
                  ListView.separated(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: lastCheck.verifications.take(5).length,
                    separatorBuilder: (context, index) =>
                        const Divider(height: 1, color: AppColors.textPrimary),
                    itemBuilder: (context, index) {
                      final verification = lastCheck.verifications[index];
                      return ListTile(
                        contentPadding: EdgeInsets.zero,
                        leading: CircleAvatar(
                          backgroundColor: verification.verification.isPresent
                              ? AppColors.buttonBackground
                              : AppColors.buttonBackground,
                          child: Icon(
                            verification.verification.isPresent
                                ? Icons.check
                                : Icons.close,
                            color: verification.verification.isPresent
                                ? AppColors.buttonText
                                : AppColors.buttonText,
                          ),
                        ),
                        title: Text(
                          verification.employee.name,
                          style: const TextStyle(fontWeight: FontWeight.bold, color: AppColors.textPrimary),
                        ),
                        subtitle: Text(
                          verification.verification.message,
                          style: const TextStyle(fontSize: 12, color: AppColors.textPrimary),
                        ),
                        trailing: verification.verification.isPresent
                            ? Text(
                                '${verification.verification.distance.toStringAsFixed(1)}م',
                                style: const TextStyle(
                                  color: AppColors.buttonText,
                                  fontWeight: FontWeight.bold,
                                ),
                              )
                            : const Icon(
                                Icons.location_off,
                                color: AppColors.buttonText,
                              ),
                      );
                    },
                  ),
                ],
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppColors.background,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            title,
            style: const TextStyle(
              fontSize: 12,
              color: AppColors.textPrimary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildVerificationHistory() {
    return Consumer<ReportsProvider>(
      builder: (context, reportsProvider, child) {
        if (reportsProvider.verificationHistory.isEmpty) {
          return Card(
            elevation: 2,
            color: AppColors.background,
            child: Padding(
              padding: const EdgeInsets.all(AppConstants.defaultPadding),
              child: Column(
                children: [
                  Icon(
                    Icons.history,
                    size: 48,
                    color: AppColors.textPrimary.withOpacity(0.5),
                  ),
                  const SizedBox(height: AppConstants.smallPadding),
                  Text(
                    'لا توجد عمليات تحقق سابقة',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: AppColors.textPrimary,
                    ),
                  ),
                ],
              ),
            ),
          );
        }

        return Card(
          elevation: 2,
          color: AppColors.background,
          child: Padding(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'سجل التحقق من الحضور',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: AppColors.buttonText,
                      ),
                    ),
                    TextButton(
                      onPressed: () {
                        context
                            .read<ReportsProvider>()
                            .clearVerificationHistory();
                      },
                      child: const Text('مسح السجل', style: TextStyle(color: AppColors.buttonText)),
                    ),
                  ],
                ),
                const SizedBox(height: AppConstants.smallPadding),
                ListView.separated(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: reportsProvider.verificationHistory.length,
                  separatorBuilder: (context, index) => const Divider(color: AppColors.textPrimary),
                  itemBuilder: (context, index) {
                    final verification =
                        reportsProvider.verificationHistory[index];
                    return ListTile(
                      contentPadding: EdgeInsets.zero,
                      leading: CircleAvatar(
                        backgroundColor: verification.isPresent
                            ? AppColors.buttonBackground
                            : AppColors.buttonBackground,
                        child: Icon(
                          verification.isPresent ? Icons.check : Icons.close,
                          color: verification.isPresent
                              ? AppColors.buttonText
                              : AppColors.buttonText,
                        ),
                      ),
                      title: Text(
                        verification.message,
                        style: const TextStyle(fontWeight: FontWeight.bold, color: AppColors.textPrimary),
                      ),
                      subtitle: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          if (verification.site != null)
                            Text('الموقع: ${verification.site!.name}', style: const TextStyle(color: AppColors.textPrimary)),
                          Text(
                            'وقت التحقق: ${_formatDateTime(verification.verificationTime)}',
                            style: const TextStyle(color: AppColors.textPrimary),
                          ),
                        ],
                      ),
                      trailing: verification.isPresent
                          ? Text(
                              '${verification.distance.toStringAsFixed(1)}م',
                              style: const TextStyle(
                                color: AppColors.buttonText,
                                fontWeight: FontWeight.bold,
                              ),
                            )
                          : const Icon(
                              Icons.location_off,
                              color: AppColors.buttonText,
                            ),
                    );
                  },
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Future<void> _checkAllEmployees() async {
    setState(() {
      _isVerifyingAll = true;
    });

    try {
      final reportsProvider = context.read<ReportsProvider>();
      final success = await reportsProvider.checkAllActiveEmployees();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              success ? 'تم فحص جميع الموظفين بنجاح' : 'فشل في فحص الموظفين',
              style: TextStyle(color: AppColors.background),
            ),
            backgroundColor: success ? AppColors.buttonText : AppColors.buttonText,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في فحص الموظفين: ${e.toString()}', style: TextStyle(color: AppColors.background)),
            backgroundColor: AppColors.buttonText,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isVerifyingAll = false;
        });
      }
    }
  }

  Future<void> _verifyIndividualEmployee(int employeeId) async {
    setState(() {
      _isCheckingLocation = true;
    });

    try {
      // Get current location
      final position = await _locationService.getCurrentPosition();

      if (!mounted) return;

      final reportsProvider = context.read<ReportsProvider>();
      final success = await reportsProvider.verifyEmployeePresence(
        userId: employeeId,
        latitude: position.latitude,
        longitude: position.longitude,
        sendAlert: true,
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              success ? 'تم التحقق من الموظف بنجاح' : 'فشل في التحقق من الموظف',
              style: TextStyle(color: AppColors.background),
            ),
            backgroundColor: success ? AppColors.buttonText : AppColors.buttonText,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في التحقق من الموظف: ${e.toString()}', style: TextStyle(color: AppColors.background)),
            backgroundColor: AppColors.buttonText,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isCheckingLocation = false;
        });
      }
    }
  }

  String _formatDateTime(String dateTimeString) {
    try {
      final dateTime = DateTime.parse(dateTimeString);
      return DateFormat('dd/MM/yyyy HH:mm').format(dateTime);
    } catch (e) {
      return dateTimeString;
    }
  }
}