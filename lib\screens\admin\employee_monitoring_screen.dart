import 'dart:async';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../constants/app_colors.dart';
import '../../services/api_service.dart';
import '../../models/models.dart';
import '../../widgets/common/index.dart';

class EmployeeMonitoringScreen extends StatefulWidget {
  const EmployeeMonitoringScreen({super.key});

  @override
  State<EmployeeMonitoringScreen> createState() =>
      _EmployeeMonitoringScreenState();
}

class _EmployeeMonitoringScreenState extends State<EmployeeMonitoringScreen>
    with TickerProviderStateMixin {
  final ApiService _apiService = ApiService();

  late TabController _tabController;
  Timer? _refreshTimer;

  List<User> _employees = [];
  MonitoringStatusResponse? _monitoringStatus;
  AllEmployeesMonitoringResponse? _allEmployeesCheck;
  Map<int, EmployeeMonitoringResponse> _individualChecks = {};

  bool _isLoading = false;
  bool _isAutoRefreshEnabled = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadInitialData();
    _startAutoRefresh();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _refreshTimer?.cancel();
    super.dispose();
  }

  void _loadInitialData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Load employees
      final employeesResponse = await _apiService.getEmployees();

      // Load monitoring status
      final statusResponse = await _apiService.getMonitoringStatus();

      // Check all active employees
      final allCheckResponse = await _apiService.checkAllActiveEmployees();

      setState(() {
        _employees = employeesResponse.data;
        _monitoringStatus = statusResponse;
        _allEmployeesCheck = allCheckResponse;
      });
    } catch (e) {
      debugPrint('Error loading monitoring data: $e');
      _showErrorSnackBar('خطأ في تحميل بيانات المراقبة: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _startAutoRefresh() {
    _refreshTimer = Timer.periodic(const Duration(seconds: 30), (timer) {
      if (_isAutoRefreshEnabled && mounted) {
        _refreshData();
      }
    });
  }

  void _refreshData() async {
    try {
      final statusResponse = await _apiService.getMonitoringStatus();
      final allCheckResponse = await _apiService.checkAllActiveEmployees();

      setState(() {
        _monitoringStatus = statusResponse;
        _allEmployeesCheck = allCheckResponse;
      });
    } catch (e) {
      debugPrint('Error refreshing data: $e');
    }
  }

  Future<void> _checkIndividualEmployee(User employee) async {
    try {
      // Get employee's last known position
      final lastPosition = await _apiService.getEmployeeLastKnownPosition(
        employee.id,
      );

      double latitude, longitude;

      if (lastPosition != null &&
          lastPosition['latitude'] != null &&
          lastPosition['longitude'] != null) {
        // Use last known position
        latitude = lastPosition['latitude'].toDouble();
        longitude = lastPosition['longitude'].toDouble();
      } else {
        // If no position available, show error
        _showErrorSnackBar('لا يوجد موقع محفوظ للموظف ${employee.name}');
        return;
      }

      final response = await _apiService.checkEmployeeOnSite(
        userId: employee.id,
        latitude: latitude,
        longitude: longitude,
      );

      setState(() {
        _individualChecks[employee.id] = response;
      });

      _showtextPrimarySnackBar('تم فحص موقع ${employee.name}');
    } catch (e) {
      debugPrint('Error checking employee: $e');
      final errorMessage = _extractErrorMessage(e, 'خطأ في فحص الموظف');
      _showErrorSnackBar(errorMessage);
    }
  }

  Future<void> _startMonitoring(User employee) async {
    try {
      final response = await _apiService.startMonitoring(
        userId: employee.id,
        intervalMinutes: 15,
      );

      _showtextPrimarySnackBar(response.messageAr ?? response.message);
      _refreshData();
    } catch (e) {
      debugPrint('Error starting monitoring: $e');
      final errorMessage = _extractErrorMessage(e, 'خطأ في بدء المراقبة');
      _showErrorSnackBar(errorMessage);
    }
  }

  Future<void> _stopMonitoring(User employee) async {
    try {
      final response = await _apiService.stopMonitoring(userId: employee.id);

      _showtextPrimarySnackBar(response.messageAr ?? response.message);
      _refreshData();
    } catch (e) {
      debugPrint('Error stopping monitoring: $e');
      final errorMessage = _extractErrorMessage(e, 'خطأ في إيقاف المراقبة');
      _showErrorSnackBar(errorMessage);
    }
  }

  String _extractErrorMessage(dynamic error, String fallbackMessage) {
    if (error is ApiException) {
      return error.message;
    }
    return '$fallbackMessage: ${error.toString()}';
  }

  void _showtextPrimarySnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.textPrimary,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.textPrimary,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('مراقبة الموظفين'),
            Text(
              '${_employees.length} موظف • ${_monitoringStatus?.totalMonitored ?? 0} مراقب',
              style: const TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.normal,
                color: Colors.white70,
              ),
            ),
          ],
        ),
        backgroundColor: AppColors.background,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          if (_allEmployeesCheck != null)
            Container(
              margin: const EdgeInsets.only(right: 8),
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.2),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(Icons.access_time, size: 14, color: Colors.white70),
                  const SizedBox(width: 4),
                  Text(
                    DateFormat('HH:mm').format(_allEmployeesCheck!.checkTime),
                    style: const TextStyle(fontSize: 12, color: Colors.white70),
                  ),
                ],
              ),
            ),
          IconButton(
            icon: Icon(_isAutoRefreshEnabled ? Icons.pause : Icons.play_arrow),
            onPressed: () {
              setState(() {
                _isAutoRefreshEnabled = !_isAutoRefreshEnabled;
              });
              if (_isAutoRefreshEnabled) {
                _startAutoRefresh();
              } else {
                _refreshTimer?.cancel();
              }
            },
            tooltip: _isAutoRefreshEnabled
                ? 'إيقاف التحديث التلقائي'
                : 'تشغيل التحديث التلقائي',
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _refreshData,
            tooltip: 'تحديث',
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: [
            Tab(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.dashboard),
                  const SizedBox(height: 2),
                  const Text('نظرة عامة'),
                ],
              ),
            ),
            Tab(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Stack(
                    children: [
                      const Icon(Icons.monitor),
                      if ((_monitoringStatus?.totalMonitored ?? 0) > 0)
                        Positioned(
                          right: -2,
                          top: -2,
                          child: Container(
                            padding: const EdgeInsets.all(2),
                            decoration: BoxDecoration(
                              color: AppColors.textPrimary,
                              borderRadius: BorderRadius.circular(6),
                            ),
                            constraints: const BoxConstraints(
                              minWidth: 12,
                              minHeight: 12,
                            ),
                            child: Text(
                              '${_monitoringStatus!.totalMonitored}',
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 8,
                                fontWeight: FontWeight.bold,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ),
                        ),
                    ],
                  ),
                  const SizedBox(height: 2),
                  const Text('المراقبة النشطة'),
                ],
              ),
            ),
            Tab(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.people_alt),
                  const SizedBox(height: 2),
                  const Text('فحص الموظفين'),
                ],
              ),
            ),
          ],
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : TabBarView(
              controller: _tabController,
              children: [
                _buildOverviewTab(),
                _buildActiveMonitoringTab(),
                _buildEmployeeCheckTab(),
              ],
            ),
    );
  }

  Widget _buildOverviewTab() {
    return RefreshIndicator(
      onRefresh: () async => _loadInitialData(),
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            _buildSummaryCards(),
            const SizedBox(height: 16),
            _buildQuickActions(),
            const SizedBox(height: 16),
            if (_allEmployeesCheck != null) _buildLastCheckSummary(),
            const SizedBox(height: 16),
            _buildEmployeeStatusOverview(),
            const SizedBox(height: 16),
            _buildActiveMonitoringOverview(),
            const SizedBox(height: 16),
            _buildRecentActivityOverview(),
            const SizedBox(height: 16),
            _buildRealTimeInfo(),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryCards() {
    final summary = _allEmployeesCheck?.summary;
    final totalEmployees = _employees.length;
    final activeMonitoring = _monitoringStatus?.totalMonitored ?? 0;
    final presentOnSite = summary?.presentOnSite ?? 0;
    final absentFromSite = summary?.absentFromSite ?? 0;
    final noAssignedSite = summary?.noAssignedSite ?? 0;

    return GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: 2,
      crossAxisSpacing: 12,
      mainAxisSpacing: 12,
      childAspectRatio: 1.3,
      children: [
        _buildEnhancedStatCard(
          title: 'إجمالي الموظفين',
          value: '$totalEmployees',
          subtitle: 'موظف مسجل',
          icon: Icons.people,
          color: AppColors.background,
          percentage: 100,
        ),
        _buildEnhancedStatCard(
          title: 'المراقبة النشطة',
          value: '$activeMonitoring',
          subtitle: 'من $totalEmployees موظف',
          icon: Icons.monitor,
          color: AppColors.textPrimary,
          percentage: totalEmployees > 0
              ? (activeMonitoring / totalEmployees * 100)
              : 0,
        ),
        _buildEnhancedStatCard(
          title: 'في الموقع',
          value: '$presentOnSite',
          subtitle: 'موظف حاضر',
          icon: Icons.location_on,
          color: AppColors.textPrimary,
          percentage: totalEmployees > 0
              ? (presentOnSite / totalEmployees * 100)
              : 0,
        ),
        _buildEnhancedStatCard(
          title: 'خارج الموقع',
          value: '$absentFromSite',
          subtitle: 'يحتاج متابعة',
          icon: Icons.location_off,
          color: AppColors.textPrimary,
          percentage: totalEmployees > 0
              ? (absentFromSite / totalEmployees * 100)
              : 0,
        ),
        if (noAssignedSite > 0)
          _buildEnhancedStatCard(
            title: 'بدون موقع',
            value: '$noAssignedSite',
            subtitle: 'غير مخصص',
            icon: Icons.help_outline,
            color: AppColors.textPrimary,
            percentage: totalEmployees > 0
                ? (noAssignedSite / totalEmployees * 100)
                : 0,
          ),
        if (summary?.notificationsSent != null &&
            summary!.notificationsSent > 0)
          _buildEnhancedStatCard(
            title: 'التنبيهات',
            value: '${summary.notificationsSent}',
            subtitle: 'تم الإرسال',
            icon: Icons.notifications,
            color: AppColors.textPrimary,
            percentage: null,
          ),
      ],
    );
  }

  Widget _buildEnhancedStatCard({
    required String title,
    required String value,
    required String subtitle,
    required IconData icon,
    required Color color,
    double? percentage,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
        border: Border.all(color: color.withOpacity(0.1), width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(icon, color: color, size: 20),
              ),
              const Spacer(),
              if (percentage != null)
                Text(
                  '${percentage.toStringAsFixed(0)}%',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: color,
                    fontWeight: FontWeight.bold,
                  ),
                ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            value,
            style: Theme.of(context).textTheme.headlineMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: Theme.of(
              context,
            ).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.w600),
          ),
          const SizedBox(height: 2),
          Text(
            subtitle,
            style: Theme.of(
              context,
            ).textTheme.bodySmall?.copyWith(color: AppColors.textSecondary),
          ),
          if (percentage != null) ...[
            const SizedBox(height: 8),
            LinearProgressIndicator(
              value: percentage / 100,
              backgroundColor: color.withOpacity(0.1),
              valueColor: AlwaysStoppedAnimation<Color>(color),
              minHeight: 3,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildQuickActions() {
    return ModernCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'إجراءات سريعة',
            style: Theme.of(
              context,
            ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: ActionButton(
                  text: 'فحص جميع الموظفين',
                  onPressed: () async {
                    try {
                      final response = await _apiService
                          .checkAllActiveEmployees();
                      setState(() {
                        _allEmployeesCheck = response;
                      });
                      _showtextPrimarySnackBar('تم فحص جميع الموظفين');
                    } catch (e) {
                      _showErrorSnackBar('خطأ في فحص الموظفين: $e');
                    }
                  },
                  variant: ActionButtonVariant.primary,
                  icon: Icons.search,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: ActionButton(
                  text: 'تحديث البيانات',
                  onPressed: _refreshData,
                  variant: ActionButtonVariant.secondary,
                  icon: Icons.refresh,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildLastCheckSummary() {
    final summary = _allEmployeesCheck!.summary;
    final checkTime = DateFormat(
      'HH:mm:ss',
    ).format(_allEmployeesCheck!.checkTime);

    return ModernCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.assessment, color: AppColors.background),
              const SizedBox(width: 8),
              Text(
                'آخر فحص شامل',
                style: Theme.of(
                  context,
                ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
              ),
              const Spacer(),
              Text(
                checkTime,
                style: Theme.of(
                  context,
                ).textTheme.bodySmall?.copyWith(color: AppColors.textSecondary),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildSummaryItem(
                  'تم فحصهم',
                  '${summary.totalChecked}',
                  AppColors.background,
                ),
              ),
              Expanded(
                child: _buildSummaryItem(
                  'في الموقع',
                  '${summary.presentOnSite}',
                  AppColors.textPrimary,
                ),
              ),
              Expanded(
                child: _buildSummaryItem(
                  'خارج الموقع',
                  '${summary.absentFromSite}',
                  AppColors.textPrimary,
                ),
              ),
              Expanded(
                child: _buildSummaryItem(
                  'تنبيهات',
                  '${summary.notificationsSent}',
                  AppColors.textPrimary,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryItem(String label, String value, Color color) {
    return Column(
      children: [
        Text(
          value,
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: Theme.of(
            context,
          ).textTheme.bodySmall?.copyWith(color: AppColors.textSecondary),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildActiveMonitoringTab() {
    return RefreshIndicator(
      onRefresh: () async => _refreshData(),
      child: _monitoringStatus?.activeMonitoring.isEmpty ?? true
          ? _buildEmptyState('لا توجد مراقبة نشطة', Icons.monitor_outlined)
          : ListView.builder(
              padding: const EdgeInsets.all(16),
              itemCount: _monitoringStatus!.activeMonitoring.length,
              itemBuilder: (context, index) {
                final monitoring = _monitoringStatus!.activeMonitoring[index];
                return _buildActiveMonitoringCard(monitoring);
              },
            ),
    );
  }

  Widget _buildActiveMonitoringCard(ActiveMonitoring monitoring) {
    final startTime = DateFormat('HH:mm').format(monitoring.startedAt);
    final expiresTime = DateFormat('HH:mm').format(monitoring.expiresAt);

    return ModernCard(
      margin: const EdgeInsets.only(bottom: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              CircleAvatar(
                backgroundColor: AppColors.textPrimary.withOpacity(0.1),
                child: Icon(Icons.person, color: AppColors.textPrimary),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      monitoring.userName,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      'بدأت في: $startTime - تنتهي في: $expiresTime',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ],
                ),
              ),
              ActionButton(
                text: 'إيقاف',
                onPressed: () async {
                  final employee = _employees.firstWhere(
                    (e) => e.id == monitoring.userId,
                    orElse: () => User(
                      id: monitoring.userId,
                      name: monitoring.userName,
                      email: '',
                      role: 'employee',
                    ),
                  );
                  await _stopMonitoring(employee);
                },
                variant: ActionButtonVariant.error,
                size: ActionButtonSize.small,
              ),
            ],
          ),
          const SizedBox(height: 12),
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: AppColors.textPrimary.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                Icon(Icons.timer, color: AppColors.textPrimary, size: 16),
                const SizedBox(width: 8),
                Text(
                  'فترة المراقبة: ${monitoring.intervalMinutes} دقيقة',
                  style: Theme.of(
                    context,
                  ).textTheme.bodySmall?.copyWith(color: AppColors.textPrimary),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmployeeCheckTab() {
    return RefreshIndicator(
      onRefresh: () async => _loadInitialData(),
      child: _employees.isEmpty
          ? _buildEmptyState('لا يوجد موظفون', Icons.people_outline)
          : ListView.builder(
              padding: const EdgeInsets.all(16),
              itemCount: _employees.length,
              itemBuilder: (context, index) {
                final employee = _employees[index];
                return _buildEmployeeCard(employee);
              },
            ),
    );
  }

  Widget _buildEmployeeCard(User employee) {
    final isMonitored =
        _monitoringStatus?.activeMonitoring.any(
          (m) => m.userId == employee.id,
        ) ??
        false;
    final individualCheck = _individualChecks[employee.id];

    return ModernCard(
      margin: const EdgeInsets.only(bottom: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              CircleAvatar(
                backgroundColor: AppColors.buttonBackground.withOpacity(0.1),
                child: Text(
                  employee.name.isNotEmpty
                      ? employee.name[0].toUpperCase()
                      : 'M',
                  style: TextStyle(
                    color: AppColors.textPrimary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      employee.name,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      employee.email,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ],
                ),
              ),
              if (isMonitored)
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: AppColors.textPrimary.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    'مراقب',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: AppColors.textPrimary,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
            ],
          ),
          const SizedBox(height: 12),
          if (individualCheck != null) ...[
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: individualCheck.isOnAssignedSite
                    ? AppColors.textPrimary.withOpacity(0.1)
                    : AppColors.textPrimary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: individualCheck.isOnAssignedSite
                      ? AppColors.textPrimary.withOpacity(0.3)
                      : AppColors.textPrimary.withOpacity(0.3),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        individualCheck.isOnAssignedSite
                            ? Icons.check_circle
                            : Icons.error,
                        color: individualCheck.isOnAssignedSite
                            ? AppColors.textPrimary
                            : AppColors.textPrimary,
                        size: 16,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          individualCheck.messageAr,
                          style: Theme.of(context).textTheme.bodySmall
                              ?.copyWith(
                                color: individualCheck.isOnAssignedSite
                                    ? AppColors.textPrimary
                                    : AppColors.textPrimary,
                              ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'المسافة: ${individualCheck.distanceFromSite.toStringAsFixed(1)} متر',
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                  Text(
                    'آخر فحص: ${DateFormat('HH:mm:ss').format(individualCheck.checkTime)}',
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                ],
              ),
            ),
            const SizedBox(height: 12),
          ],
          Row(
            children: [
              Expanded(
                child: ActionButton(
                  text: 'فحص الموقع',
                  onPressed: () => _checkIndividualEmployee(employee),
                  variant: ActionButtonVariant.secondary,
                  size: ActionButtonSize.small,
                  icon: Icons.location_searching,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: ActionButton(
                  text: isMonitored ? 'إيقاف المراقبة' : 'بدء المراقبة',
                  onPressed: isMonitored
                      ? () => _stopMonitoring(employee)
                      : () => _startMonitoring(employee),
                  variant: isMonitored
                      ? ActionButtonVariant.error
                      : ActionButtonVariant.primary,
                  size: ActionButtonSize.small,
                  icon: isMonitored ? Icons.stop : Icons.play_arrow,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildEmployeeStatusOverview() {
    return ModernCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.people_alt, color: AppColors.background),
              const SizedBox(width: 8),
              Text(
                'حالة الموظفين',
                style: Theme.of(
                  context,
                ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
              ),
            ],
          ),
          const SizedBox(height: 16),
          if (_employees.isNotEmpty) ...[
            ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: _employees.take(5).length, // Show first 5 employees
              separatorBuilder: (context, index) => const SizedBox(height: 8),
              itemBuilder: (context, index) {
                final employee = _employees[index];
                final isMonitored =
                    _monitoringStatus?.activeMonitoring.any(
                      (m) => m.userId == employee.id,
                    ) ??
                    false;
                final individualCheck = _individualChecks[employee.id];

                return Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: AppColors.background,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: AppColors.textHint.withOpacity(0.3),
                    ),
                  ),
                  child: Row(
                    children: [
                      CircleAvatar(
                        radius: 20,
                        backgroundColor: AppColors.background.withOpacity(
                          0.1,
                        ),
                        child: Text(
                          employee.name.isNotEmpty
                              ? employee.name[0].toUpperCase()
                              : 'M',
                          style: TextStyle(
                            color: AppColors.background,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              employee.name,
                              style: Theme.of(context).textTheme.titleSmall
                                  ?.copyWith(fontWeight: FontWeight.bold),
                            ),
                            const SizedBox(height: 2),
                            Text(
                              employee.email,
                              style: Theme.of(context).textTheme.bodySmall
                                  ?.copyWith(color: AppColors.textSecondary),
                            ),
                            if (individualCheck != null) ...[
                              const SizedBox(height: 4),
                              Row(
                                children: [
                                  Icon(
                                    individualCheck.isOnAssignedSite
                                        ? Icons.location_on
                                        : Icons.location_off,
                                    size: 14,
                                    color: individualCheck.isOnAssignedSite
                                        ? AppColors.textPrimary
                                        : AppColors.textPrimary,
                                  ),
                                  const SizedBox(width: 4),
                                  Text(
                                    '${individualCheck.distanceFromSite.toStringAsFixed(0)}م',
                                    style: Theme.of(context).textTheme.bodySmall
                                        ?.copyWith(
                                          color:
                                              individualCheck.isOnAssignedSite
                                              ? AppColors.textPrimary
                                              : AppColors.textPrimary,
                                        ),
                                  ),
                                  const SizedBox(width: 8),
                                  Container(
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 6,
                                      vertical: 2,
                                    ),
                                    decoration: BoxDecoration(
                                      color: individualCheck.isOnAssignedSite
                                          ? AppColors.textPrimary.withOpacity(
                                              0.1,
                                            )
                                          : AppColors.textPrimary.withOpacity(
                                              0.1,
                                            ),
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    child: Text(
                                      individualCheck.messageAr,
                                      style: Theme.of(context)
                                          .textTheme
                                          .bodySmall
                                          ?.copyWith(
                                            color:
                                                individualCheck.isOnAssignedSite
                                                ? AppColors.textPrimary
                                                : AppColors.textPrimary,
                                            fontSize: 10,
                                          ),
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ],
                        ),
                      ),
                      Column(
                        children: [
                          if (isMonitored)
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 4,
                              ),
                              decoration: BoxDecoration(
                                color: AppColors.textPrimary.withOpacity(0.1),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Text(
                                'مراقب',
                                style: Theme.of(context).textTheme.bodySmall
                                    ?.copyWith(
                                      color: AppColors.textPrimary,
                                      fontWeight: FontWeight.w600,
                                    ),
                              ),
                            ),
                          if (individualCheck != null) ...[
                            const SizedBox(height: 4),
                            Icon(
                              individualCheck.isOnAssignedSite
                                  ? Icons.check_circle
                                  : Icons.error,
                              size: 16,
                              color: individualCheck.isOnAssignedSite
                                  ? AppColors.textPrimary
                                  : AppColors.textPrimary,
                            ),
                          ],
                        ],
                      ),
                    ],
                  ),
                );
              },
            ),
            if (_employees.length > 5) ...[
              const SizedBox(height: 12),
              Center(
                child: TextButton(
                  onPressed: () => _tabController.animateTo(2),
                  child: Text('عرض جميع الموظفين (${_employees.length})'),
                ),
              ),
            ],
          ] else ...[
            Center(
              child: Text(
                'لا يوجد موظفون',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: AppColors.textSecondary,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildActiveMonitoringOverview() {
    final activeMonitoring = _monitoringStatus?.activeMonitoring ?? [];

    return ModernCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.monitor, color: AppColors.textPrimary),
              const SizedBox(width: 8),
              Text(
                'المراقبة النشطة',
                style: Theme.of(
                  context,
                ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
              ),
              const Spacer(),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: AppColors.textPrimary.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  '${activeMonitoring.length}',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppColors.textPrimary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          if (activeMonitoring.isNotEmpty) ...[
            ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: activeMonitoring.take(3).length, // Show first 3
              separatorBuilder: (context, index) => const SizedBox(height: 8),
              itemBuilder: (context, index) {
                final monitoring = activeMonitoring[index];
                final startTime = DateFormat(
                  'HH:mm',
                ).format(monitoring.startedAt);
                final duration = DateTime.now().difference(
                  monitoring.startedAt,
                );

                return Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: AppColors.textPrimary.withOpacity(0.05),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: AppColors.textPrimary.withOpacity(0.2),
                    ),
                  ),
                  child: Row(
                    children: [
                      Icon(Icons.person, color: AppColors.textPrimary, size: 20),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              monitoring.userName,
                              style: Theme.of(context).textTheme.titleSmall
                                  ?.copyWith(fontWeight: FontWeight.bold),
                            ),
                            const SizedBox(height: 2),
                            Text(
                              'بدأت في: $startTime',
                              style: Theme.of(context).textTheme.bodySmall
                                  ?.copyWith(color: AppColors.textSecondary),
                            ),
                          ],
                        ),
                      ),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          Text(
                            '${duration.inMinutes}د',
                            style: Theme.of(context).textTheme.bodySmall
                                ?.copyWith(
                                  color: AppColors.textPrimary,
                                  fontWeight: FontWeight.bold,
                                ),
                          ),
                          Text(
                            'كل ${monitoring.intervalMinutes}د',
                            style: Theme.of(context).textTheme.bodySmall
                                ?.copyWith(color: AppColors.textSecondary),
                          ),
                        ],
                      ),
                    ],
                  ),
                );
              },
            ),
            if (activeMonitoring.length > 3) ...[
              const SizedBox(height: 12),
              Center(
                child: TextButton(
                  onPressed: () => _tabController.animateTo(1),
                  child: Text(
                    'عرض جميع المراقبة النشطة (${activeMonitoring.length})',
                  ),
                ),
              ),
            ],
          ] else ...[
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: AppColors.textSecondary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(Icons.info_outline, color: AppColors.textSecondary),
                  const SizedBox(width: 12),
                  Text(
                    'لا توجد مراقبة نشطة حالياً',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildRecentActivityOverview() {
    return ModernCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.history, color: AppColors.textPrimary),
              const SizedBox(width: 8),
              Text(
                'النشاط الأخير',
                style: Theme.of(
                  context,
                ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
              ),
            ],
          ),
          const SizedBox(height: 16),
          if (_allEmployeesCheck != null) ...[
            _buildActivityItem(
              'فحص شامل للموظفين',
              'تم فحص ${_allEmployeesCheck!.summary.totalChecked} موظف',
              DateFormat('HH:mm').format(_allEmployeesCheck!.checkTime),
              Icons.search,
              AppColors.textPrimary,
            ),
            const SizedBox(height: 8),
            _buildActivityItem(
              'موظفون في الموقع',
              '${_allEmployeesCheck!.summary.presentOnSite} موظف',
              'آخر تحديث',
              Icons.location_on,
              AppColors.textPrimary,
            ),
            const SizedBox(height: 8),
            _buildActivityItem(
              'موظفون خارج الموقع',
              '${_allEmployeesCheck!.summary.absentFromSite} موظف',
              'يحتاج متابعة',
              Icons.location_off,
              AppColors.textPrimary,
            ),
            if (_allEmployeesCheck!.summary.notificationsSent > 0) ...[
              const SizedBox(height: 8),
              _buildActivityItem(
                'تنبيهات مرسلة',
                '${_allEmployeesCheck!.summary.notificationsSent} تنبيه',
                'تم الإرسال',
                Icons.notifications,
                AppColors.textPrimary,
              ),
            ],
          ] else ...[
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: AppColors.textSecondary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(Icons.info_outline, color: AppColors.textSecondary),
                  const SizedBox(width: 12),
                  Text(
                    'لا يوجد نشاط حديث',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildActivityItem(
    String title,
    String subtitle,
    String time,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.2)),
      ),
      child: Row(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: Theme.of(
                    context,
                  ).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 2),
                Text(
                  subtitle,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),
          Text(
            time,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: color,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRealTimeInfo() {
    return ModernCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.access_time, color: AppColors.textPrimary),
              const SizedBox(width: 8),
              Text(
                'معلومات الوقت الحقيقي',
                style: Theme.of(
                  context,
                ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
              ),
              const Spacer(),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: _isAutoRefreshEnabled
                      ? AppColors.textPrimary.withOpacity(0.1)
                      : AppColors.textSecondary.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      _isAutoRefreshEnabled ? Icons.refresh : Icons.pause,
                      size: 14,
                      color: _isAutoRefreshEnabled
                          ? AppColors.textPrimary
                          : AppColors.textSecondary,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      _isAutoRefreshEnabled ? 'تحديث تلقائي' : 'متوقف',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: _isAutoRefreshEnabled
                            ? AppColors.textPrimary
                            : AppColors.textSecondary,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildInfoItem(
                  'الوقت الحالي',
                  DateFormat('HH:mm:ss').format(DateTime.now()),
                  Icons.schedule,
                  AppColors.textPrimary,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildInfoItem(
                  'آخر تحديث',
                  _allEmployeesCheck != null
                      ? DateFormat(
                          'HH:mm',
                        ).format(_allEmployeesCheck!.checkTime)
                      : 'لم يتم',
                  Icons.update,
                  AppColors.textPrimary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _buildInfoItem(
                  'معدل الحضور',
                  _employees.isNotEmpty
                      ? '${((_allEmployeesCheck?.summary.presentOnSite ?? 0) / _employees.length * 100).toStringAsFixed(0)}%'
                      : '0%',
                  Icons.trending_up,
                  AppColors.textPrimary,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildInfoItem(
                  'التحديث التالي',
                  _isAutoRefreshEnabled ? '30 ثانية' : 'يدوي',
                  Icons.timer,
                  AppColors.textPrimary,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildInfoItem(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: 16),
              const SizedBox(width: 6),
              Expanded(
                child: Text(
                  title,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState(String message, IconData icon) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, size: 64, color: AppColors.textSecondary),
          const SizedBox(height: 16),
          Text(
            message,
            style: Theme.of(
              context,
            ).textTheme.titleMedium?.copyWith(color: AppColors.textSecondary),
          ),
        ],
      ),
    );
  }
}