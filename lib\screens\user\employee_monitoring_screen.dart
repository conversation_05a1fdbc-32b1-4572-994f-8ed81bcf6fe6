import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import 'package:geolocator/geolocator.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_constants.dart';
import '../../providers/providers.dart';
import '../../services/location_service.dart';
import '../../services/geofencing_service.dart';

/// Écran de surveillance pour les employés
class EmployeeMonitoringScreen extends StatefulWidget {
  const EmployeeMonitoringScreen({super.key});

  @override
  State<EmployeeMonitoringScreen> createState() => _EmployeeMonitoringScreenState();
}

class _EmployeeMonitoringScreenState extends State<EmployeeMonitoringScreen> {
  final LocationService _locationService = LocationService();
  final GeofencingService _geofencingService = GeofencingService();
  
  bool _isLocationEnabled = false;
  bool _isMonitoringActive = false;
  Position? _currentPosition;
  String? _currentSiteName;
  double? _distanceToSite;

  @override
  void initState() {
    super.initState();
    _initializeMonitoring();
  }

  @override
  void dispose() {
    _geofencingService.stopMonitoring();
    super.dispose();
  }

  /// Initialise la surveillance
  Future<void> _initializeMonitoring() async {
    await _checkLocationPermission();
    await _loadData();
  }

  /// Vérifie les permissions de localisation
  Future<void> _checkLocationPermission() async {
    try {
      final hasPermission = await _locationService.hasPermission();
      setState(() {
        _isLocationEnabled = hasPermission;
      });

      if (hasPermission) {
        await _getCurrentLocation();
      }
    } catch (e) {
      debugPrint('Erreur lors de la vérification des permissions: $e');
    }
  }

  /// Obtient la position actuelle
  Future<void> _getCurrentLocation() async {
    try {
      final position = await _locationService.getCurrentPosition();
      setState(() {
        _currentPosition = position;
      });
      await _checkNearestSite();
    } catch (e) {
      debugPrint('Erreur lors de l\'obtention de la position: $e');
    }
  }

  /// Vérifie le site le plus proche
  Future<void> _checkNearestSite() async {
    if (_currentPosition == null) return;

    try {
      final sitesProvider = context.read<SitesProvider>();
      final sites = sitesProvider.sites;

      double? minDistance;
      String? nearestSiteName;

      for (final site in sites) {
        final distance = Geolocator.distanceBetween(
          _currentPosition!.latitude,
          _currentPosition!.longitude,
          site.latitude,
          site.longitude,
        );

        if (minDistance == null || distance < minDistance) {
          minDistance = distance;
          nearestSiteName = site.nom;
        }
      }

      setState(() {
        _distanceToSite = minDistance;
        _currentSiteName = nearestSiteName;
      });
    } catch (e) {
      debugPrint('Erreur lors de la vérification du site le plus proche: $e');
    }
  }

  /// Charge les données
  Future<void> _loadData() async {
    if (!mounted) return;
    
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<AttendanceProvider>().loadCurrentAttendance();
      context.read<SitesProvider>().loadSitesData();
    });
  }

  /// Active/désactive la surveillance
  Future<void> _toggleMonitoring() async {
    if (!_isLocationEnabled) {
      await _requestLocationPermission();
      return;
    }

    setState(() {
      _isMonitoringActive = !_isMonitoringActive;
    });

    if (_isMonitoringActive) {
      await _startMonitoring();
    } else {
      await _stopMonitoring();
    }
  }

  /// Démarre la surveillance
  Future<void> _startMonitoring() async {
    try {
      final sitesProvider = context.read<SitesProvider>();
      final sites = sitesProvider.sites;

      // Configurer les callbacks de géofencing
      _geofencingService.onSiteEntered = _onSiteEntered;
      _geofencingService.onSiteExited = _onSiteExited;
      _geofencingService.onLocationUpdate = _onLocationUpdate;

      // Démarrer la surveillance
      await _geofencingService.startMonitoring(sites);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم تفعيل المراقبة بنجاح'),
            backgroundColor: AppColors.background,
          ),
        );
      }
    } catch (e) {
      setState(() {
        _isMonitoringActive = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل في تفعيل المراقبة: ${e.toString()}'),
            backgroundColor: AppColors.buttonText,
          ),
        );
      }
    }
  }

  /// Arrête la surveillance
  Future<void> _stopMonitoring() async {
    _geofencingService.stopMonitoring();
    
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم إيقاف المراقبة'),
          backgroundColor: AppColors.background,
        ),
      );
    }
  }

  /// Demande les permissions de localisation
  Future<void> _requestLocationPermission() async {
    try {
      final granted = await _locationService.requestPermission();
      setState(() {
        _isLocationEnabled = granted;
      });

      if (granted) {
        await _getCurrentLocation();
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('يجب السماح بالوصول للموقع لتفعيل المراقبة'),
              backgroundColor: AppColors.buttonText,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل في طلب إذن الموقع: ${e.toString()}'),
            backgroundColor: AppColors.buttonText,
          ),
        );
      }
    }
  }

  /// Callback عند دخول موقع
  void _onSiteEntered(site) {
    setState(() {
      _currentSiteName = site.nom;
      _distanceToSite = 0;
    });

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('🎯 دخلت منطقة: ${site.nom}'),
          backgroundColor: AppColors.background,
          action: SnackBarAction(
            label: 'تسجيل حضور',
            textColor: AppColors.textPrimary,
            onPressed: () => _quickCheckIn(site),
          ),
        ),
      );
    }
  }

  /// Callback عند مغادرة موقع
  void _onSiteExited(site) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('🚪 غادرت منطقة: ${site.nom}'),
          backgroundColor: AppColors.buttonText,
        ),
      );
    }
  }

  /// Callback عند تحديث الموقع
  void _onLocationUpdate(Position position) {
    setState(() {
      _currentPosition = position;
    });
    _checkNearestSite();
  }

  /// تسجيل حضور سريع
  Future<void> _quickCheckIn(site) async {
    if (_currentPosition == null) return;

    try {
      final success = await context.read<AttendanceProvider>().checkInEmployee(
        siteId: site.id,
        latitude: _currentPosition!.latitude,
        longitude: _currentPosition!.longitude,
      );

      if (success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم تسجيل الحضور بنجاح'),
            backgroundColor: AppColors.background,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل في تسجيل الحضور: ${e.toString()}'),
            backgroundColor: AppColors.buttonText,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('مراقبة الحضور'),
        backgroundColor: AppColors.background,
        foregroundColor: AppColors.textPrimary,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            color: AppColors.textPrimary,
            onPressed: () {
              _loadData();
              _getCurrentLocation();
            },
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: () async {
          await _loadData();
          await _getCurrentLocation();
        },
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildMonitoringStatus(),
              const SizedBox(height: AppConstants.defaultPadding),
              _buildLocationInfo(),
              const SizedBox(height: AppConstants.defaultPadding),
              _buildCurrentAttendance(),
              const SizedBox(height: AppConstants.defaultPadding),
              _buildQuickActions(),
            ],
          ),
        ),
      ),
    );
  }

  /// بناء حالة المراقبة
  Widget _buildMonitoringStatus() {
    return Card(
      color: AppColors.background,
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  _isMonitoringActive ? Icons.visibility : Icons.visibility_off,
                  color: _isMonitoringActive ? AppColors.textPrimary : AppColors.textPrimary.withOpacity(0.5),
                ),
                const SizedBox(width: AppConstants.smallPadding),
                Text(
                  'حالة المراقبة',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppColors.textPrimary,
                  ),
                ),
                const Spacer(),
                Switch(
                  value: _isMonitoringActive,
                  onChanged: (_) => _toggleMonitoring(),
                  activeColor: AppColors.textPrimary,
                  activeTrackColor: AppColors.textPrimary.withOpacity(0.3),
                  inactiveThumbColor: AppColors.textPrimary.withOpacity(0.5),
                  inactiveTrackColor: AppColors.textPrimary.withOpacity(0.1),
                ),
              ],
            ),
            const SizedBox(height: AppConstants.smallPadding),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: _isMonitoringActive
                    ? AppColors.textPrimary.withOpacity(0.1)
                    : AppColors.textPrimary.withOpacity(0.05),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(
                    _isMonitoringActive ? Icons.check_circle : Icons.info,
                    color: _isMonitoringActive ? AppColors.textPrimary : AppColors.textPrimary.withOpacity(0.5),
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      _isMonitoringActive
                          ? 'المراقبة نشطة - سيتم تتبع موقعك تلقائياً'
                          : 'المراقبة غير نشطة - قم بتفعيلها لتتبع الحضور',
                      style: TextStyle(
                        color: _isMonitoringActive ? AppColors.textPrimary : AppColors.textPrimary.withOpacity(0.5),
                        fontSize: 14,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            if (!_isLocationEnabled) ...[
              const SizedBox(height: AppConstants.smallPadding),
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: AppColors.buttonText.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Icon(Icons.location_off, color: AppColors.buttonText, size: 20),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'يجب تفعيل خدمات الموقع لاستخدام المراقبة',
                        style: TextStyle(color: AppColors.buttonText, fontSize: 14),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// بناء معلومات الموقع
  Widget _buildLocationInfo() {
    return Card(
      color: AppColors.background,
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.location_on, color: AppColors.buttonText),
                const SizedBox(width: AppConstants.smallPadding),
                Text(
                  'معلومات الموقع',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppColors.textPrimary,
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            if (_currentPosition != null) ...[
              _buildLocationItem(
                'خط العرض',
                _currentPosition!.latitude.toStringAsFixed(6),
                Icons.my_location,
              ),
              _buildLocationItem(
                'خط الطول',
                _currentPosition!.longitude.toStringAsFixed(6),
                Icons.my_location,
              ),
              _buildLocationItem(
                'دقة GPS',
                '${_currentPosition!.accuracy.toStringAsFixed(1)} متر',
                Icons.gps_fixed,
              ),
              if (_currentSiteName != null) ...[
                _buildLocationItem(
                  'أقرب موقع',
                  _currentSiteName!,
                  Icons.business,
                ),
                if (_distanceToSite != null)
                  _buildLocationItem(
                    'المسافة',
                    '${_distanceToSite!.toStringAsFixed(0)} متر',
                    Icons.straighten,
                  ),
              ],
            ] else ...[
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: AppColors.textPrimary.withOpacity(0.05),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  children: [
                    Icon(Icons.location_searching,
                         color: AppColors.textPrimary.withOpacity(0.5), size: 32),
                    const SizedBox(height: 8),
                    Text(
                      'جاري تحديد الموقع...',
                      style: TextStyle(color: AppColors.textPrimary.withOpacity(0.5)),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// بناء عنصر معلومات الموقع
  Widget _buildLocationItem(String title, String value, IconData icon) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Icon(icon, size: 16, color: AppColors.textPrimary),
          const SizedBox(width: 8),
          Text(
            '$title: ',
            style: const TextStyle(fontWeight: FontWeight.w500, color: AppColors.textPrimary),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(color: AppColors.textPrimary),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء الحضور الحالي
  Widget _buildCurrentAttendance() {
    return Consumer<AttendanceProvider>(
      builder: (context, provider, child) {
        return Card(
          color: AppColors.background,
          child: Padding(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(Icons.access_time, color: AppColors.buttonText),
                    const SizedBox(width: AppConstants.smallPadding),
                    Text(
                      'الحضور الحالي',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: AppColors.textPrimary,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: AppConstants.defaultPadding),
                if (provider.hasActivePointage) ...[
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: AppColors.textPrimary.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: AppColors.textPrimary.withOpacity(0.3)),
                    ),
                    child: Column(
                      children: [
                        Icon(Icons.work, color: AppColors.textPrimary, size: 32),
                        const SizedBox(height: 8),
                        Text(
                          'أنت حالياً في العمل',
                          style: TextStyle(
                            color: AppColors.textPrimary,
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                        ),
                        if (provider.activePointage?.debutPointage != null) ...[
                          const SizedBox(height: 4),
                          Text(
                            'بدأت في: ${DateFormat('HH:mm').format(provider.activePointage!.debutPointage)}',
                            style: TextStyle(color: AppColors.textPrimary),
                          ),
                        ],
                      ],
                    ),
                  ),
                ] else ...[
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: AppColors.textPrimary.withOpacity(0.05),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Column(
                      children: [
                        Icon(Icons.work_off, color: AppColors.textPrimary.withOpacity(0.5), size: 32),
                        const SizedBox(height: 8),
                        Text(
                          'لم تسجل حضورك بعد',
                          style: TextStyle(
                            color: AppColors.textPrimary.withOpacity(0.5),
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          'اقترب من موقع العمل لتسجيل الحضور',
                          style: TextStyle(color: AppColors.textPrimary.withOpacity(0.5)),
                        ),
                      ],
                    ),
                  ),
                ],
              ],
            ),
          ),
        );
      },
    );
  }

  /// بناء الإجراءات السريعة
  Widget _buildQuickActions() {
    return Card(
      color: AppColors.background,
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.flash_on, color: AppColors.buttonText),
                const SizedBox(width: AppConstants.smallPadding),
                Text(
                  'إجراءات سريعة',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppColors.textPrimary,
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            Row(
              children: [
                Expanded(
                  child: _buildQuickActionButton(
                    'تحديث الموقع',
                    Icons.my_location,
                    AppColors.buttonText,
                    () => _getCurrentLocation(),
                  ),
                ),
                const SizedBox(width: AppConstants.smallPadding),
                Expanded(
                  child: _buildQuickActionButton(
                    'عرض المواقع',
                    Icons.map,
                    AppColors.buttonText,
                    () => _showSitesMap(),
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppConstants.smallPadding),
            Row(
              children: [
                Expanded(
                  child: _buildQuickActionButton(
                    'سجل الحضور',
                    Icons.history,
                    AppColors.buttonText,
                    () => _showAttendanceHistory(),
                  ),
                ),
                const SizedBox(width: AppConstants.smallPadding),
                Expanded(
                  child: _buildQuickActionButton(
                    'الإعدادات',
                    Icons.settings,
                    AppColors.buttonText,
                    () => _showSettings(),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// بناء زر إجراء سريع
  Widget _buildQuickActionButton(String title, IconData icon, Color color, VoidCallback onPressed) {
    return ElevatedButton.icon(
      onPressed: onPressed,
      icon: Icon(icon, size: 18, color: AppColors.textPrimary),
      label: Text(title, style: TextStyle(fontSize: 12, color: AppColors.textPrimary)),
      style: ElevatedButton.styleFrom(
        backgroundColor: color.withOpacity(0.8),
        foregroundColor: AppColors.textPrimary,
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        ),
      ),
    );
  }

  /// عرض خريطة المواقع
  void _showSitesMap() {
    // TODO: Implémenter l'affichage de la carte
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('سيتم إضافة خريطة المواقع قريباً'),
        backgroundColor: AppColors.background,
      ),
    );
  }

  /// عرض سجل الحضور
  void _showAttendanceHistory() {
    Navigator.pushNamed(context, '/attendance-history');
  }

  /// عرض الإعدادات
  void _showSettings() {
    // TODO: Implémenter écran des paramètres
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('سيتم إضافة الإعدادات قريباً'),
        backgroundColor: AppColors.background,
      ),
    );
  }
}