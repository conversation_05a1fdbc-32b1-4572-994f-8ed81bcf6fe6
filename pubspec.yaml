name: timetrackr
description: "Timetrackr"
publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: '>=3.9.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  cupertino_icons: ^1.0.8
  material_design_icons_flutter: ^7.0.7296
  flutter_svg: ^2.0.10+1
  http: ^1.2.2
  dio: ^5.7.0
  provider: ^6.1.2
  shared_preferences: ^2.3.2
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  geolocator: ^10.1.0
  geocoding: ^3.0.0
  permission_handler: ^12.0.1 # Updated from 10.2.0
  flutter_form_builder: ^10.1.0
  form_builder_validators: ^11.0.0
  flutter_spinkit: ^5.2.1
  cached_network_image: ^3.4.1
  shimmer: ^3.0.0
  intl: ^0.20.2
  json_annotation: ^4.9.0
  equatable: ^2.0.5
  uuid: ^4.5.1
  google_maps_flutter: ^2.7.0 # Updated from 2.5.3
  location: ^5.0.3
  pdf: ^3.11.1
  printing: ^5.13.4
  file_picker: ^8.1.6
  path_provider: ^2.1.5
  open_file: ^3.5.10 # Updated from 3.3.2
  share_plus: ^10.0.0 # Updated from 7.2.1
  flutter_local_notifications: ^18.0.1
  firebase_messaging: ^15.1.6
  image_picker: ^1.1.2
  qr_flutter: ^4.1.0
  table_calendar: ^3.1.2
  lottie: ^3.2.0
  animations: ^2.0.11
  flutter_staggered_animations: ^1.1.1
  connectivity_plus: ^6.1.0
  internet_connection_checker: ^1.0.0+1
  excel: ^4.0.6
  ntp: ^2.0.0
  url_launcher: ^6.3.0 # Updated from 6.1.0
  data_table_2: ^2.6.0
  retry: ^3.1.2
  logger: ^2.6.1

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0
  build_runner: ^2.4.13
  json_serializable: ^6.8.0
  hive_generator: ^2.0.1
  flutter_launcher_icons: ^0.14.4

flutter:
  uses-material-design: true
  generate: true
  assets:
    - assets/images/
    - assets/icons/
    - assets/logo/

flutter_intl:
  enabled: true
  class_name: S
  main_locale: ar
  arb_dir: lib/l10n
  output_dir: lib/generated

flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/logo/app_icon_1024.png"
  min_sdk_android: 21 # android min sdk min:16, default 21
  remove_alpha_ios: true
  adaptive_icon_background: "#2C3E50"
  adaptive_icon_foreground: "assets/logo/app_icon_1024.png"
  web:
    generate: true
    image_path: "assets/logo/app_icon_1024.png"
    background_color: "#2C3E50"
    theme_color: "#2C3E50"
  windows:
    generate: true
    image_path: "assets/logo/app_icon_1024.png"
    icon_size: 48 # min:48, max:256, default: 48
  macos:
    generate: true
    image_path: "assets/logo/app_icon_1024.png"