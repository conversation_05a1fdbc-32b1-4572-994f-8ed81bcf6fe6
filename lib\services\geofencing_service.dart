import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:geolocator/geolocator.dart';
import '../models/models.dart';
import '../constants/app_constants.dart';
import 'location_service.dart';

/// Service de géofencing pour détecter automatiquement l'entrée/sortie des zones de sites
class GeofencingService {
  static final GeofencingService _instance = GeofencingService._internal();
  factory GeofencingService() => _instance;
  GeofencingService._internal();

  final LocationService _locationService = LocationService();
  StreamSubscription<Position>? _positionSubscription;
  
  List<Site> _monitoredSites = [];
  Site? _currentSite;
  bool _isMonitoring = false;
  
  // Callbacks pour les événements de géofencing
  Function(Site site)? onSiteEntered;
  Function(Site site)? onSiteExited;
  Function(Position position)? onLocationUpdate;

  // Getters
  bool get isMonitoring => _isMonitoring;
  Site? get currentSite => _currentSite;
  List<Site> get monitoredSites => List.unmodifiable(_monitoredSites);

  /// Démarre le monitoring des sites
  Future<void> startMonitoring(List<Site> sites) async {
    if (_isMonitoring) {
      debugPrint('Geofencing: Already monitoring');
      return;
    }

    _monitoredSites = sites;
    _isMonitoring = true;

    try {
      // Démarre le tracking de localisation
      await _locationService.startLocationTracking();
      
      // Écoute les mises à jour de position
      _positionSubscription = _locationService.positionStream.listen(
        _onPositionUpdate,
        onError: (error) {
          debugPrint('Geofencing: Position stream error: $error');
        },
      );

      debugPrint('Geofencing: Started monitoring ${sites.length} sites');
    } catch (e) {
      _isMonitoring = false;
      debugPrint('Geofencing: Error starting monitoring: $e');
      rethrow;
    }
  }

  /// Arrête le monitoring
  void stopMonitoring() {
    if (!_isMonitoring) return;

    _positionSubscription?.cancel();
    _positionSubscription = null;
    _locationService.stopLocationTracking();
    
    _isMonitoring = false;
    _currentSite = null;
    
    debugPrint('Geofencing: Stopped monitoring');
  }

  /// Met à jour la liste des sites à monitorer
  void updateMonitoredSites(List<Site> sites) {
    _monitoredSites = sites;
    debugPrint('Geofencing: Updated monitored sites to ${sites.length}');
  }

  /// Traite les mises à jour de position
  void _onPositionUpdate(Position position) {
    // Notifie les listeners de la mise à jour de position
    onLocationUpdate?.call(position);

    // Trouve le site le plus proche dans le rayon autorisé
    Site? nearestSite = _findNearestSiteInRange(position);
    
    // Vérifie les changements de site
    if (nearestSite?.id != _currentSite?.id) {
      // Sortie du site précédent
      if (_currentSite != null) {
        debugPrint('Geofencing: Exited site: ${_currentSite!.name}');
        onSiteExited?.call(_currentSite!);
      }
      
      // Entrée dans un nouveau site
      if (nearestSite != null) {
        debugPrint('Geofencing: Entered site: ${nearestSite.name}');
        onSiteEntered?.call(nearestSite);
      }
      
      _currentSite = nearestSite;
    }
  }

  /// Trouve le site le plus proche dans le rayon autorisé
  Site? _findNearestSiteInRange(Position position) {
    Site? nearestSite;
    double minDistance = double.infinity;

    for (final site in _monitoredSites) {
      final distance = Geolocator.distanceBetween(
        position.latitude,
        position.longitude,
        site.latitude,
        site.longitude,
      );

      // Vérifie si le site est dans le rayon autorisé
      if (distance <= AppConstants.maxAllowedDistance && distance < minDistance) {
        minDistance = distance;
        nearestSite = site;
      }
    }

    return nearestSite;
  }

  /// Obtient la distance au site le plus proche
  double? getDistanceToNearestSite(Position position) {
    if (_monitoredSites.isEmpty) return null;

    double minDistance = double.infinity;
    
    for (final site in _monitoredSites) {
      final distance = Geolocator.distanceBetween(
        position.latitude,
        position.longitude,
        site.latitude,
        site.longitude,
      );
      
      if (distance < minDistance) {
        minDistance = distance;
      }
    }

    return minDistance == double.infinity ? null : minDistance;
  }

  /// Vérifie si une position est dans la zone d'un site
  bool isPositionInSiteRange(Position position, Site site) {
    final distance = Geolocator.distanceBetween(
      position.latitude,
      position.longitude,
      site.latitude,
      site.longitude,
    );
    
    return distance <= AppConstants.maxAllowedDistance;
  }

  /// Obtient les sites dans le rayon autorisé pour une position
  List<Site> getSitesInRange(Position position) {
    return _monitoredSites.where((site) {
      final distance = Geolocator.distanceBetween(
        position.latitude,
        position.longitude,
        site.latitude,
        site.longitude,
      );
      return distance <= AppConstants.maxAllowedDistance;
    }).toList();
  }

  /// Nettoie les ressources
  void dispose() {
    stopMonitoring();
    onSiteEntered = null;
    onSiteExited = null;
    onLocationUpdate = null;
  }
}

/// Événement de géofencing
class GeofencingEvent {
  final GeofencingEventType type;
  final Site site;
  final Position position;
  final DateTime timestamp;

  const GeofencingEvent({
    required this.type,
    required this.site,
    required this.position,
    required this.timestamp,
  });

  @override
  String toString() {
    return 'GeofencingEvent{type: $type, site: ${site.name}, timestamp: $timestamp}';
  }
}

/// Types d'événements de géofencing
enum GeofencingEventType {
  entered,
  exited,
}

/// Extension pour obtenir le texte en arabe
extension GeofencingEventTypeExtension on GeofencingEventType {
  String get arabicText {
    switch (this) {
      case GeofencingEventType.entered:
        return 'دخول المنطقة';
      case GeofencingEventType.exited:
        return 'خروج من المنطقة';
    }
  }
}
