import 'package:json_annotation/json_annotation.dart';
import 'package:equatable/equatable.dart';
import 'user.dart';
import 'site.dart';

part 'assignment.g.dart';

@JsonSerializable()
class Assignment extends Equatable {
  final int id;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'user_id')
  final int userId;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'site_id')
  final int siteId;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'created_at')
  final String? createdAt;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'updated_at')
  final String? updatedAt;
  final User? user;
  final Site? site;

  const Assignment({
    required this.id,
    required this.userId,
    required this.siteId,
    this.createdAt,
    this.updatedAt,
    this.user,
    this.site,
  });

  factory Assignment.fromJson(Map<String, dynamic> json) => _$AssignmentFromJson(json);

  Map<String, dynamic> toJson() => _$AssignmentToJson(this);

  Assignment copyWith({
    int? id,
    int? userId,
    int? siteId,
    String? createdAt,
    String? updatedAt,
    User? user,
    Site? site,
  }) {
    return Assignment(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      siteId: siteId ?? this.siteId,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      user: user ?? this.user,
      site: site ?? this.site,
    );
  }

  String get userName => user?.name ?? 'غير محدد';
  String get userEmail => user?.email ?? 'غير محدد';
  String get userRole => user?.displayRole ?? 'غير محدد';
  String get siteName => site?.name ?? 'غير محدد';
  String get siteCoordinates => site?.coordinates ?? 'غير محدد';

  String get displayCreatedAt {
    if (createdAt == null) return 'غير محدد';
    try {
      final dateTime = DateTime.parse(createdAt!);
      return '${dateTime.day.toString().padLeft(2, '0')}/${dateTime.month.toString().padLeft(2, '0')}/${dateTime.year}';
    } catch (e) {
      return 'غير صحيح';
    }
  }

  @override
  List<Object?> get props => [id, userId, siteId, createdAt, updatedAt, user, site];

  @override
  String toString() {
    return 'Assignment{id: $id, userId: $userId, siteId: $siteId, userName: $userName, siteName: $siteName}';
  }
}

@JsonSerializable()
class UserAssignment extends Equatable {
  final User user;
  @JsonKey(name: 'assigned_sites')
  final List<Site> assignedSites;
  @JsonKey(name: 'active_pointages')
  final int activePointages;
  @JsonKey(name: 'total_pointages')
  final int totalPointages;

  const UserAssignment({
    required this.user,
    required this.assignedSites,
    required this.activePointages,
    required this.totalPointages,
  });

  factory UserAssignment.fromJson(Map<String, dynamic> json) =>
      _$UserAssignmentFromJson(json);

  Map<String, dynamic> toJson() => _$UserAssignmentToJson(this);

  int get userId => user.id;
  String get userName => user.name;
  String get userEmail => user.email;
  String get userRole => user.displayRole;
  int get assignedSitesCount => assignedSites.length;
  bool get hasAssignedSites => assignedSites.isNotEmpty;
  bool get hasActivePointages => activePointages > 0;

  String get assignedSitesText {
    if (assignedSites.isEmpty) return 'لا توجد مواقع مخصصة';
    if (assignedSites.length == 1) return assignedSites.first.name;
    return '${assignedSites.length} مواقع';
  }

  String get statusText {
    if (hasActivePointages) return 'نشط';
    if (totalPointages > 0) return 'غير نشط';
    return 'لم يبدأ';
  }

  String get statusColor {
    if (hasActivePointages) return 'success';
    if (totalPointages > 0) return 'warning';
    return 'info';
  }

  @override
  List<Object?> get props => [user, assignedSites, activePointages, totalPointages];

  @override
  String toString() {
    return 'UserAssignment{userId: $userId, userName: $userName, sitesCount: $assignedSitesCount}';
  }
}

@JsonSerializable()
class SiteAssignment extends Equatable {
  final Site site;
  @JsonKey(name: 'assigned_users')
  final List<User> assignedUsers;
  @JsonKey(name: 'active_pointages')
  final int activePointages;
  @JsonKey(name: 'total_pointages')
  final int totalPointages;

  const SiteAssignment({
    required this.site,
    required this.assignedUsers,
    required this.activePointages,
    required this.totalPointages,
  });

  factory SiteAssignment.fromJson(Map<String, dynamic> json) =>
      _$SiteAssignmentFromJson(json);

  Map<String, dynamic> toJson() => _$SiteAssignmentToJson(this);

  int get siteId => site.id;
  String get siteName => site.name;
  String get siteCoordinates => site.coordinates;
  int get assignedUsersCount => assignedUsers.length;
  bool get hasAssignedUsers => assignedUsers.isNotEmpty;
  bool get hasActivePointages => activePointages > 0;

  String get assignedUsersText {
    if (assignedUsers.isEmpty) return 'لا يوجد موظفون مخصصون';
    if (assignedUsers.length == 1) return assignedUsers.first.name;
    return '${assignedUsers.length} موظفين';
  }

  String get statusText {
    if (hasActivePointages) return 'نشط';
    if (totalPointages > 0) return 'غير نشط';
    return 'لم يستخدم';
  }

  String get statusColor {
    if (hasActivePointages) return 'success';
    if (totalPointages > 0) return 'warning';
    return 'info';
  }

  @override
  List<Object?> get props => [site, assignedUsers, activePointages, totalPointages];

  @override
  String toString() {
    return 'SiteAssignment{siteId: $siteId, siteName: $siteName, usersCount: $assignedUsersCount}';
  }
}

@JsonSerializable()
class AssignmentStats extends Equatable {
  @JsonKey(name: 'total_assignments')
  final int totalAssignments;
  @JsonKey(name: 'active_assignments')
  final int activeAssignments;
  @JsonKey(name: 'users_with_assignments')
  final int usersWithAssignments;
  @JsonKey(name: 'sites_with_assignments')
  final int sitesWithAssignments;
  @JsonKey(name: 'unassigned_users')
  final int unassignedUsers;
  @JsonKey(name: 'unassigned_sites')
  final int unassignedSites;

  const AssignmentStats({
    required this.totalAssignments,
    required this.activeAssignments,
    required this.usersWithAssignments,
    required this.sitesWithAssignments,
    required this.unassignedUsers,
    required this.unassignedSites,
  });

  factory AssignmentStats.fromJson(Map<String, dynamic> json) =>
      _$AssignmentStatsFromJson(json);

  Map<String, dynamic> toJson() => _$AssignmentStatsToJson(this);

  double get assignmentUtilization {
    if (totalAssignments == 0) return 0.0;
    return (activeAssignments / totalAssignments) * 100;
  }

  String get utilizationText => '${assignmentUtilization.toStringAsFixed(1)}%';

  @override
  List<Object?> get props => [
        totalAssignments,
        activeAssignments,
        usersWithAssignments,
        sitesWithAssignments,
        unassignedUsers,
        unassignedSites,
      ];

  @override
  String toString() {
    return 'AssignmentStats{total: $totalAssignments, active: $activeAssignments, utilization: $utilizationText}';
  }
}
