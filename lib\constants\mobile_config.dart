import 'dart:io';

/// Mobile-specific configuration constants
class MobileConfig {
  // Platform detection
  static bool get isAndroid => Platform.isAndroid;
  static bool get isIOS => Platform.isIOS;
  static bool get isMobile => Platform.isAndroid || Platform.isIOS;

  // Network configuration for mobile
  static const Duration mobileConnectionTimeout = Duration(seconds: 45);
  static const Duration mobileReceiveTimeout = Duration(seconds: 45);
  static const Duration mobileSendTimeout = Duration(seconds: 45);

  // Location configuration for mobile
  static const Duration mobileLocationTimeout = Duration(seconds: 15);
  static const double mobileLocationAccuracyThreshold = 50.0; // meters
  static const int mobileLocationRetryAttempts = 5;

  // File storage configuration
  static const String mobileReportsFolder = 'ClockIn_Reports';
  static const int maxFileSizeBytes = 50 * 1024 * 1024; // 50MB
  static const List<String> supportedFileTypes = [
    'xlsx',
    'csv',
    'pdf',
    'jpg',
    'jpeg',
    'png',
  ];

  // UI configuration for mobile
  static const double mobileMinTouchTarget = 44.0; // iOS HIG minimum
  static const double mobilePadding = 16.0;
  static const double mobileSmallPadding = 8.0;
  static const double mobileLargePadding = 24.0;

  // Performance configuration
  static const int maxCacheSize = 100; // Maximum cached items
  static const Duration cacheExpiry = Duration(hours: 2);
  static const int maxConcurrentRequests = 3;

  // Battery optimization
  static const Duration backgroundSyncInterval = Duration(minutes: 30);
  static const Duration foregroundSyncInterval = Duration(minutes: 5);

  // Error handling
  static const int maxRetryAttempts = 3;
  static const Duration retryDelay = Duration(seconds: 2);
  static const Duration exponentialBackoffBase = Duration(seconds: 1);

  // Security
  static const bool enforceHttps = false; // Set to true in production
  static const Duration tokenRefreshThreshold = Duration(minutes: 5);

  // Offline support
  static const int maxOfflineActions = 50;
  static const Duration offlineDataRetention = Duration(days: 7);

  // Notification configuration
  static const String notificationChannelId = 'timetrackr_mobile';
  static const String notificationChannelName = 'Timetrackr Mobile';
  static const String notificationChannelDescription =
      'Mobile notifications for Timetrackr app';

  // Development/Debug settings
  static const bool enableDebugLogging = true;
  static const bool enablePerformanceMonitoring = true;
  static const bool enableCrashReporting = false; // Enable in production

  // Platform-specific settings
  static Map<String, dynamic> get androidSettings => {
    'usesCleartextTraffic': true,
    'requestLegacyExternalStorage': true,
    'enableBackgroundLocation': false,
  };

  static Map<String, dynamic> get iosSettings => {
    'backgroundModes': ['location', 'background-fetch'],
    'requiresFullScreen': false,
    'supportedInterfaceOrientations': ['portrait'],
  };

  // Helper methods
  static Duration getTimeoutForPlatform() {
    if (isAndroid) {
      return const Duration(seconds: 30);
    } else if (isIOS) {
      return const Duration(seconds: 25);
    }
    return const Duration(seconds: 30);
  }

  static double getLocationAccuracyForPlatform() {
    if (isAndroid) {
      return 20.0; // Android typically has better GPS
    } else if (isIOS) {
      return 30.0; // iOS location services
    }
    return 50.0;
  }

  static String getFilePathForPlatform(String filename) {
    if (isAndroid) {
      return '/storage/emulated/0/Download/$mobileReportsFolder/$filename';
    } else if (isIOS) {
      return 'Documents/$mobileReportsFolder/$filename';
    }
    return filename;
  }
}
