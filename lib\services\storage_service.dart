import 'package:hive_flutter/hive_flutter.dart';
import 'package:flutter/foundation.dart';
import '../constants/app_constants.dart';
import '../models/models.dart';

class StorageService {
  static final StorageService _instance = StorageService._internal();
  factory StorageService() => _instance;
  StorageService._internal();

  Box? _userBox;
  Box? _settingsBox;
  Box? _cacheBox;
  Box? _attendanceBox;

  // Initialize Hive and open boxes
  Future<void> initialize() async {
    try {
      await Hive.initFlutter();

      // Open boxes
      _userBox = await Hive.openBox(AppConstants.userBox);
      _settingsBox = await Hive.openBox(AppConstants.settingsBox);
      _cacheBox = await Hive.openBox(AppConstants.cacheBox);
      _attendanceBox = await Hive.openBox(AppConstants.attendanceBox);

      debugPrint('Storage: Initialized successfully');
    } catch (e) {
      debugPrint('Storage: Initialization failed: $e');
      rethrow;
    }
  }

  // User data methods
  Future<void> saveUser(User user) async {
    try {
      await _userBox?.put('current_user', user.toJson());
      debugPrint('Storage: User saved');
    } catch (e) {
      debugPrint('Storage: Error saving user: $e');
    }
  }

  User? getUser() {
    try {
      final userData = _userBox?.get('current_user');
      if (userData != null) {
        return User.fromJson(Map<String, dynamic>.from(userData));
      }
      return null;
    } catch (e) {
      debugPrint('Storage: Error getting user: $e');
      return null;
    }
  }

  Future<void> clearUser() async {
    try {
      await _userBox?.delete('current_user');
      debugPrint('Storage: User cleared');
    } catch (e) {
      debugPrint('Storage: Error clearing user: $e');
    }
  }

  // Token methods
  Future<void> saveToken(String token) async {
    try {
      await _userBox?.put('auth_token', token);
      debugPrint('Storage: Token saved');
    } catch (e) {
      debugPrint('Storage: Error saving token: $e');
    }
  }

  String? getToken() {
    try {
      return _userBox?.get('auth_token');
    } catch (e) {
      debugPrint('Storage: Error getting token: $e');
      return null;
    }
  }

  Future<void> clearToken() async {
    try {
      await _userBox?.delete('auth_token');
      debugPrint('Storage: Token cleared');
    } catch (e) {
      debugPrint('Storage: Error clearing token: $e');
    }
  }

  // Settings methods
  Future<void> saveSetting(String key, dynamic value) async {
    try {
      await _settingsBox?.put(key, value);
      debugPrint('Storage: Setting saved - $key');
    } catch (e) {
      debugPrint('Storage: Error saving setting $key: $e');
    }
  }

  T? getSetting<T>(String key, [T? defaultValue]) {
    try {
      return _settingsBox?.get(key, defaultValue: defaultValue);
    } catch (e) {
      debugPrint('Storage: Error getting setting $key: $e');
      return defaultValue;
    }
  }

  Future<void> clearSetting(String key) async {
    try {
      await _settingsBox?.delete(key);
      debugPrint('Storage: Setting cleared - $key');
    } catch (e) {
      debugPrint('Storage: Error clearing setting $key: $e');
    }
  }

  Future<void> clearAllSettings() async {
    try {
      await _settingsBox?.clear();
      debugPrint('Storage: All settings cleared');
    } catch (e) {
      debugPrint('Storage: Error clearing all settings: $e');
    }
  }

  // Cache methods
  Future<void> saveToCache(
    String key,
    dynamic data, {
    Duration? expiration,
  }) async {
    try {
      final cacheData = {
        'data': data,
        'timestamp': DateTime.now().millisecondsSinceEpoch,
        'expiration': expiration?.inMilliseconds,
      };
      await _cacheBox?.put(key, cacheData);
      debugPrint('Storage: Data cached - $key');
    } catch (e) {
      debugPrint('Storage: Error caching data $key: $e');
    }
  }

  T? getFromCache<T>(String key) {
    try {
      final cacheData = _cacheBox?.get(key);
      if (cacheData == null) return null;

      final timestamp = cacheData['timestamp'] as int?;
      final expiration = cacheData['expiration'] as int?;

      if (timestamp != null && expiration != null) {
        final cacheTime = DateTime.fromMillisecondsSinceEpoch(timestamp);
        final expirationTime = cacheTime.add(
          Duration(milliseconds: expiration),
        );

        if (DateTime.now().isAfter(expirationTime)) {
          // Cache expired, remove it
          _cacheBox?.delete(key);
          return null;
        }
      }

      return cacheData['data'] as T?;
    } catch (e) {
      debugPrint('Storage: Error getting cached data $key: $e');
      return null;
    }
  }

  Future<void> clearCache(String key) async {
    try {
      await _cacheBox?.delete(key);
      debugPrint('Storage: Cache cleared - $key');
    } catch (e) {
      debugPrint('Storage: Error clearing cache $key: $e');
    }
  }

  Future<void> clearAllCache() async {
    try {
      await _cacheBox?.clear();
      debugPrint('Storage: All cache cleared');
    } catch (e) {
      debugPrint('Storage: Error clearing all cache: $e');
    }
  }

  // Attendance data methods
  Future<void> saveAttendanceData(String key, List<Pointage> pointages) async {
    try {
      final data = pointages.map((p) => p.toJson()).toList();
      await _attendanceBox?.put(key, data);
      debugPrint('Storage: Attendance data saved - $key');
    } catch (e) {
      debugPrint('Storage: Error saving attendance data $key: $e');
    }
  }

  List<Pointage> getAttendanceData(String key) {
    try {
      final data = _attendanceBox?.get(key);
      if (data != null && data is List) {
        return data
            .map((item) => Pointage.fromJson(Map<String, dynamic>.from(item)))
            .toList();
      }
      return [];
    } catch (e) {
      debugPrint('Storage: Error getting attendance data $key: $e');
      return [];
    }
  }

  Future<void> clearAttendanceData(String key) async {
    try {
      await _attendanceBox?.delete(key);
      debugPrint('Storage: Attendance data cleared - $key');
    } catch (e) {
      debugPrint('Storage: Error clearing attendance data $key: $e');
    }
  }

  // Sites data methods
  Future<void> saveSites(List<Site> sites) async {
    try {
      final data = sites.map((s) => s.toJson()).toList();
      await saveToCache(
        'sites_list',
        data,
        expiration: AppConstants.cacheExpiration,
      );
      debugPrint('Storage: Sites saved to cache');
    } catch (e) {
      debugPrint('Storage: Error saving sites: $e');
    }
  }

  List<Site> getSites() {
    try {
      final data = getFromCache<List>('sites_list');
      if (data != null) {
        return data
            .map((item) => Site.fromJson(Map<String, dynamic>.from(item)))
            .toList();
      }
      return [];
    } catch (e) {
      debugPrint('Storage: Error getting sites: $e');
      return [];
    }
  }

  // Employees data methods
  Future<void> saveEmployees(List<User> employees) async {
    try {
      final data = employees.map((e) => e.toJson()).toList();
      await saveToCache(
        'employees_list',
        data,
        expiration: AppConstants.cacheExpiration,
      );
      debugPrint('Storage: Employees saved to cache');
    } catch (e) {
      debugPrint('Storage: Error saving employees: $e');
    }
  }

  List<User> getEmployees() {
    try {
      final data = getFromCache<List>('employees_list');
      if (data != null) {
        return data
            .map((item) => User.fromJson(Map<String, dynamic>.from(item)))
            .toList();
      }
      return [];
    } catch (e) {
      debugPrint('Storage: Error getting employees: $e');
      return [];
    }
  }

  // App preferences
  Future<void> setFirstLaunch(bool isFirst) async {
    await saveSetting('is_first_launch', isFirst);
  }

  bool isFirstLaunch() {
    return getSetting<bool>('is_first_launch', true) ?? true;
  }

  Future<void> setThemeMode(String mode) async {
    await saveSetting('theme_mode', mode);
  }

  String getThemeMode() {
    return getSetting<String>('theme_mode', 'light') ?? 'light';
  }

  Future<void> setLanguage(String language) async {
    await saveSetting('language', language);
  }

  String getLanguage() {
    return getSetting<String>('language', 'ar') ?? 'ar';
  }

  // Offline data management
  Future<void> saveOfflinePointage(Map<String, dynamic> pointageData) async {
    try {
      final offlineData = getFromCache<List>('offline_pointages') ?? [];
      offlineData.add(pointageData);
      await saveToCache('offline_pointages', offlineData);
      debugPrint('Storage: Offline pointage saved');
    } catch (e) {
      debugPrint('Storage: Error saving offline pointage: $e');
    }
  }

  List<Map<String, dynamic>> getOfflinePointages() {
    try {
      final data = getFromCache<List>('offline_pointages');
      if (data != null) {
        return data.cast<Map<String, dynamic>>();
      }
      return [];
    } catch (e) {
      debugPrint('Storage: Error getting offline pointages: $e');
      return [];
    }
  }

  Future<void> clearOfflinePointages() async {
    await clearCache('offline_pointages');
  }

  // Storage statistics
  Map<String, dynamic> getStorageStats() {
    return {
      'user_box_length': _userBox?.length ?? 0,
      'settings_box_length': _settingsBox?.length ?? 0,
      'cache_box_length': _cacheBox?.length ?? 0,
      'attendance_box_length': _attendanceBox?.length ?? 0,
    };
  }

  // Clear all data
  Future<void> clearAllData() async {
    try {
      await _userBox?.clear();
      await _settingsBox?.clear();
      await _cacheBox?.clear();
      await _attendanceBox?.clear();
      debugPrint('Storage: All data cleared');
    } catch (e) {
      debugPrint('Storage: Error clearing all data: $e');
    }
  }

  // Close all boxes
  Future<void> close() async {
    try {
      await _userBox?.close();
      await _settingsBox?.close();
      await _cacheBox?.close();
      await _attendanceBox?.close();
      debugPrint('Storage: All boxes closed');
    } catch (e) {
      debugPrint('Storage: Error closing boxes: $e');
    }
  }
}
