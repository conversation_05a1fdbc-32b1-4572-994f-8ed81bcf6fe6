import 'dart:async';
import 'dart:math';
import 'package:geolocator/geolocator.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:flutter/foundation.dart';
import '../constants/app_constants.dart';
import '../models/models.dart';

class LocationService {
  static final LocationService _instance = LocationService._internal();
  factory LocationService() => _instance;
  LocationService._internal();

  StreamSubscription<Position>? _positionStreamSubscription;
  Position? _lastKnownPosition;
  final StreamController<Position> _positionController = StreamController<Position>.broadcast();

  // Get current position with retry mechanism
  Future<Position> getCurrentPosition({int maxRetries = 3}) async {
    int attempts = 0;

    while (attempts < maxRetries) {
      try {
        attempts++;

        // Check permissions
        if (!await _checkPermissions()) {
          throw LocationException('Location permission denied');
        }

        // Check if location services are enabled
        if (!await Geolocator.isLocationServiceEnabled()) {
          throw LocationException('Location services are disabled');
        }

        // Get current position with progressive accuracy fallback
        LocationAccuracy accuracy = attempts == 1
            ? LocationAccuracy.high
            : attempts == 2
                ? LocationAccuracy.medium
                : LocationAccuracy.low;

        final position = await Geolocator.getCurrentPosition(
          desiredAccuracy: accuracy,
          timeLimit: Duration(seconds: AppConstants.locationTimeoutSeconds + (attempts * 5)),
        );

        // Validate accuracy (more lenient on retries)
        double accuracyThreshold = AppConstants.gpsAccuracyThreshold * attempts;
        if (position.accuracy > accuracyThreshold && attempts < maxRetries) {
          debugPrint('Location: Accuracy ${position.accuracy.toStringAsFixed(1)}m too low, retrying...');
          await Future.delayed(Duration(seconds: 2));
          continue;
        }

        _lastKnownPosition = position;
        debugPrint('Location: Got current position - ${position.latitude}, ${position.longitude} (accuracy: ${position.accuracy.toStringAsFixed(1)}m, attempt: $attempts)');
        return position;
      } catch (e) {
        debugPrint('Location: Error getting current position (attempt $attempts): $e');

        if (attempts >= maxRetries) {
          if (e is LocationException) {
            rethrow;
          }
          throw LocationException('Failed to get current location after $maxRetries attempts: $e');
        }

        // Wait before retry
        await Future.delayed(Duration(seconds: 2 * attempts));
      }
    }

    throw LocationException('Failed to get current location after $maxRetries attempts');
  }

  // Get last known position
  Position? get lastKnownPosition => _lastKnownPosition;

  // Get best available position (current or last known)
  Future<Position> getBestAvailablePosition() async {
    try {
      // Try to get current position first
      return await getCurrentPosition(maxRetries: 1);
    } catch (e) {
      debugPrint('Location: Could not get current position, using last known: $e');

      // Fall back to last known position if available
      if (_lastKnownPosition != null) {
        final age = DateTime.now().difference(DateTime.fromMillisecondsSinceEpoch(_lastKnownPosition!.timestamp.millisecondsSinceEpoch));

        // Only use last known position if it's less than 10 minutes old
        if (age.inMinutes < 10) {
          debugPrint('Location: Using last known position (${age.inMinutes} minutes old)');
          return _lastKnownPosition!;
        } else {
          debugPrint('Location: Last known position too old (${age.inMinutes} minutes)');
        }
      }

      // If no last known position or it's too old, rethrow the original error
      rethrow;
    }
  }

  // Check if location permissions are granted
  Future<bool> _checkPermissions() async {
    LocationPermission permission = await Geolocator.checkPermission();
    
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
    }
    
    if (permission == LocationPermission.deniedForever) {
      // Open app settings for user to manually enable permissions
      await openAppSettings();
      return false;
    }
    
    return permission == LocationPermission.whileInUse || 
           permission == LocationPermission.always;
  }

  // Request location permissions
  Future<bool> requestPermissions() async {
    try {
      final status = await Permission.location.request();
      
      if (status.isDenied) {
        return false;
      }
      
      if (status.isPermanentlyDenied) {
        await openAppSettings();
        return false;
      }
      
      return status.isGranted;
    } catch (e) {
      debugPrint('Location: Error requesting permissions: $e');
      return false;
    }
  }

  // Check if location services are enabled
  Future<bool> isLocationServiceEnabled() async {
    return await Geolocator.isLocationServiceEnabled();
  }

  /// Vérifie si l'application a les permissions de localisation
  Future<bool> hasPermission() async {
    final permission = await Geolocator.checkPermission();
    return permission == LocationPermission.always ||
           permission == LocationPermission.whileInUse;
  }

  /// Demande les permissions de localisation
  Future<bool> requestPermission() async {
    final permission = await Geolocator.requestPermission();
    return permission == LocationPermission.always ||
           permission == LocationPermission.whileInUse;
  }

  /// Obtient un stream de positions
  Stream<Position> getPositionStream() {
    return Geolocator.getPositionStream(
      locationSettings: const LocationSettings(
        accuracy: LocationAccuracy.high,
        distanceFilter: 10,
      ),
    );
  }

  // Open location settings
  Future<void> openLocationSettings() async {
    await Geolocator.openLocationSettings();
  }

  // Calculate distance between two points using Haversine formula
  double calculateDistance(
    double lat1,
    double lon1,
    double lat2,
    double lon2,
  ) {
    return Geolocator.distanceBetween(lat1, lon1, lat2, lon2);
  }

  // Check if position is within range of a site
  bool isWithinRange(Position position, Site site, {double? maxDistance}) {
    final distance = calculateDistance(
      position.latitude,
      position.longitude,
      site.latitude,
      site.longitude,
    );
    
    final allowedDistance = maxDistance ?? AppConstants.maxAllowedDistance;
    return distance <= allowedDistance;
  }

  // Get distance to site
  double getDistanceToSite(Position position, Site site) {
    return calculateDistance(
      position.latitude,
      position.longitude,
      site.latitude,
      site.longitude,
    );
  }

  // Start location tracking
  Future<void> startLocationTracking() async {
    try {
      if (!await _checkPermissions()) {
        throw LocationException('Location permission denied');
      }

      if (!await isLocationServiceEnabled()) {
        throw LocationException('Location services are disabled');
      }

      const locationSettings = LocationSettings(
        accuracy: LocationAccuracy.high,
        distanceFilter: 10, // Update every 10 meters
      );

      _positionStreamSubscription = Geolocator.getPositionStream(
        locationSettings: locationSettings,
      ).listen(
        (Position position) {
          _lastKnownPosition = position;
          _positionController.add(position);
          debugPrint('Location: Position updated - ${position.latitude}, ${position.longitude}');
        },
        onError: (error) {
          debugPrint('Location: Position stream error: $error');
        },
      );

      debugPrint('Location: Started location tracking');
    } catch (e) {
      debugPrint('Location: Error starting location tracking: $e');
      rethrow;
    }
  }

  // Stop location tracking
  void stopLocationTracking() {
    _positionStreamSubscription?.cancel();
    _positionStreamSubscription = null;
    debugPrint('Location: Stopped location tracking');
  }

  // Get position stream
  Stream<Position> get positionStream => _positionController.stream;

  // Check location for pointage
  Future<LocationCheckResult> checkLocationForPointage(Site site) async {
    try {
      final position = await getCurrentPosition();
      final distance = getDistanceToSite(position, site);
      final inRange = distance <= AppConstants.maxAllowedDistance;

      return LocationCheckResult(
        position: position,
        site: site,
        distance: distance,
        inRange: inRange,
        maxDistance: AppConstants.maxAllowedDistance,
      );
    } catch (e) {
      debugPrint('Location: Error checking location for pointage: $e');
      rethrow;
    }
  }

  // Get location status text
  String getLocationStatusText(double distance, bool inRange) {
    if (inRange) {
      return 'داخل النطاق المسموح (${distance.toStringAsFixed(1)} متر)';
    } else {
      return 'خارج النطاق المسموح (${distance.toStringAsFixed(1)} متر)';
    }
  }

  // Get location accuracy text
  String getAccuracyText(double accuracy) {
    if (accuracy <= 10) {
      return 'دقة عالية (${accuracy.toStringAsFixed(1)} متر)';
    } else if (accuracy <= 50) {
      return 'دقة متوسطة (${accuracy.toStringAsFixed(1)} متر)';
    } else {
      return 'دقة منخفضة (${accuracy.toStringAsFixed(1)} متر)';
    }
  }

  // Format coordinates for display
  String formatCoordinates(double latitude, double longitude) {
    return '${latitude.toStringAsFixed(6)}, ${longitude.toStringAsFixed(6)}';
  }

  // Validate GPS accuracy
  bool isAccuracyAcceptable(double accuracy) {
    return accuracy <= AppConstants.gpsAccuracyThreshold;
  }

  // Get bearing between two points
  double getBearing(double lat1, double lon1, double lat2, double lon2) {
    final dLon = (lon2 - lon1) * pi / 180;
    final lat1Rad = lat1 * pi / 180;
    final lat2Rad = lat2 * pi / 180;

    final y = sin(dLon) * cos(lat2Rad);
    final x = cos(lat1Rad) * sin(lat2Rad) - sin(lat1Rad) * cos(lat2Rad) * cos(dLon);

    final bearing = atan2(y, x) * 180 / pi;
    return (bearing + 360) % 360;
  }

  // Get direction text
  String getDirectionText(double bearing) {
    if (bearing >= 337.5 || bearing < 22.5) return 'شمال';
    if (bearing >= 22.5 && bearing < 67.5) return 'شمال شرق';
    if (bearing >= 67.5 && bearing < 112.5) return 'شرق';
    if (bearing >= 112.5 && bearing < 157.5) return 'جنوب شرق';
    if (bearing >= 157.5 && bearing < 202.5) return 'جنوب';
    if (bearing >= 202.5 && bearing < 247.5) return 'جنوب غرب';
    if (bearing >= 247.5 && bearing < 292.5) return 'غرب';
    if (bearing >= 292.5 && bearing < 337.5) return 'شمال غرب';
    return 'غير محدد';
  }

  // Dispose resources
  void dispose() {
    stopLocationTracking();
    _positionController.close();
  }
}

// Location check result class
class LocationCheckResult {
  final Position position;
  final Site site;
  final double distance;
  final bool inRange;
  final double maxDistance;

  LocationCheckResult({
    required this.position,
    required this.site,
    required this.distance,
    required this.inRange,
    required this.maxDistance,
  });

  String get statusText => inRange ? 'داخل النطاق' : 'خارج النطاق';
  String get distanceText => '${distance.toStringAsFixed(1)} متر';
  String get accuracyText => '${position.accuracy.toStringAsFixed(1)} متر';
  String get coordinatesText => '${position.latitude.toStringAsFixed(6)}, ${position.longitude.toStringAsFixed(6)}';
}

// Location exception class
class LocationException implements Exception {
  final String message;
  final String? code;

  const LocationException(this.message, [this.code]);

  @override
  String toString() => 'LocationException: $message';
}
