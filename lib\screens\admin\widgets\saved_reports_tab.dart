import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../../../constants/app_colors.dart';
import '../../../constants/app_constants.dart';
import '../../../providers/providers.dart';
import '../../../models/models.dart';

/// Onglet pour gérer les rapports Excel sauvegardés
class SavedReportsTab extends StatefulWidget {
  const SavedReportsTab({super.key});

  @override
  State<SavedReportsTab> createState() => _SavedReportsTabState();
}

class _SavedReportsTabState extends State<SavedReportsTab> {
  List<FileInfo> _savedReports = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadSavedReports();
  }

  Future<void> _loadSavedReports() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final reportsProvider = context.read<ReportsProvider>();
      final reports = await reportsProvider.getSavedReports();

      if (mounted) {
        setState(() {
          _savedReports = reports;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل في تحميل الملفات المحفوظة: $e', style: TextStyle(color: AppColors.background)),
            backgroundColor: AppColors.buttonText,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: RefreshIndicator(
        onRefresh: _loadSavedReports,
        child: _isLoading
            ? const Center(child: CircularProgressIndicator(color: AppColors.buttonText))
            : _savedReports.isEmpty
            ? _buildEmptyState()
            : _buildReportsList(),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _loadSavedReports,
        backgroundColor: AppColors.buttonBackground,
        child: const Icon(Icons.refresh, color: AppColors.buttonText),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.folder_open, size: 64, color: AppColors.textPrimary),
          const SizedBox(height: 16),
          Text(
            'لا توجد ملفات محفوظة',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(color: AppColors.textPrimary),
          ),
          const SizedBox(height: 8),
          Text(
            'قم بتحميل تقارير Excel لتظهر هنا',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(color: AppColors.textPrimary),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: _loadSavedReports,
            icon: const Icon(Icons.refresh, color: AppColors.textPrimary),
            label: const Text('تحديث', style: TextStyle(color: AppColors.textPrimary)),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.buttonBackground,
              foregroundColor: AppColors.buttonText,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildReportsList() {
    return ListView.builder(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      itemCount: _savedReports.length,
      itemBuilder: (context, index) {
        final report = _savedReports[index];
        return _buildReportCard(report);
      },
    );
  }

  Widget _buildReportCard(FileInfo report) {
    return Card(
      elevation: 2,
      margin: const EdgeInsets.only(bottom: AppConstants.defaultPadding),
      color: AppColors.background,
      child: ListTile(
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: AppColors.buttonBackground,
            borderRadius: BorderRadius.circular(8),
          ),
          child: const Icon(Icons.table_chart, color: AppColors.buttonText),
        ),
        title: Text(
          report.name,
          style: const TextStyle(fontWeight: FontWeight.bold, color: AppColors.textPrimary),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 4),
            Text('الحجم: ${_formatFileSize(report.size)}', style: TextStyle(color: AppColors.textPrimary)),
            Text('تاريخ الحفظ: ${_formatDateTime(report.modifiedDate)}', style: TextStyle(color: AppColors.textPrimary)),
          ],
        ),
        trailing: PopupMenuButton<String>(
          onSelected: (value) => _handleMenuAction(value, report),
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'open',
              child: ListTile(
                leading: Icon(Icons.open_in_new, color: AppColors.buttonText),
                title: Text('فتح', style: TextStyle(color: AppColors.textPrimary)),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            const PopupMenuItem(
              value: 'share',
              child: ListTile(
                leading: Icon(Icons.share, color: AppColors.buttonText),
                title: Text('مشاركة', style: TextStyle(color: AppColors.textPrimary)),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            const PopupMenuItem(
              value: 'delete',
              child: ListTile(
                leading: Icon(Icons.delete, color: AppColors.buttonText),
                title: Text('حذف', style: TextStyle(color: AppColors.buttonText)),
                contentPadding: EdgeInsets.zero,
              ),
            ),
          ],
        ),
        onTap: () => _openReport(report),
      ),
    );
  }

  Future<void> _handleMenuAction(String action, FileInfo report) async {
    switch (action) {
      case 'open':
        await _openReport(report);
        break;
      case 'share':
        await _shareReport(report);
        break;
      case 'delete':
        await _deleteReport(report);
        break;
    }
  }

  Future<void> _openReport(FileInfo report) async {
    try {
      final reportsProvider = context.read<ReportsProvider>();
      final opened = await reportsProvider.openExcelFile(report.path);

      if (!opened && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('لا يمكن فتح الملف. تأكد من وجود تطبيق Excel', style: TextStyle(color: AppColors.background)),
            backgroundColor: AppColors.buttonText,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل في فتح الملف: $e', style: TextStyle(color: AppColors.background)),
            backgroundColor: AppColors.buttonText,
          ),
        );
      }
    }
  }

  Future<void> _shareReport(FileInfo report) async {
    try {
      final reportsProvider = context.read<ReportsProvider>();
      final shared = await reportsProvider.shareExcelFile(
        report.path,
        report.name,
      );

      if (shared && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم مشاركة الملف بنجاح', style: TextStyle(color: AppColors.background)),
            backgroundColor: AppColors.buttonText,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل في مشاركة الملف: $e', style: TextStyle(color: AppColors.background)),
            backgroundColor: AppColors.buttonText,
          ),
        );
      }
    }
  }

  Future<void> _deleteReport(FileInfo report) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppColors.background,
        title: const Text('تأكيد الحذف', style: TextStyle(color: AppColors.textPrimary)),
        content: Text('هل تريد حذف الملف "${report.name}"؟', style: TextStyle(color: AppColors.textPrimary)),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء', style: TextStyle(color: AppColors.buttonText)),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.buttonText,
              foregroundColor: AppColors.textPrimary,
            ),
            child: const Text('حذف', style: TextStyle(color: AppColors.textPrimary)),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        final reportsProvider = context.read<ReportsProvider>();
        final deleted = await reportsProvider.deleteReport(report.path);

        if (deleted && mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم حذف الملف بنجاح', style: TextStyle(color: AppColors.background)),
              backgroundColor: AppColors.buttonText,
            ),
          );

          // Recharger la liste
          await _loadSavedReports();
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('فشل في حذف الملف: $e', style: TextStyle(color: AppColors.background)),
              backgroundColor: AppColors.buttonText,
            ),
          );
        }
      }
    }
  }

  String _formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024)
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  String _formatDateTime(DateTime dateTime) {
    return DateFormat('dd/MM/yyyy HH:mm').format(dateTime);
  }
}