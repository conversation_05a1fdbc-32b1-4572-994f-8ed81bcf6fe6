import 'package:flutter/material.dart';
import '../../constants/app_colors.dart';

enum StatusType { success, warning, error, info, neutral }

enum StatusSize { small, medium, large }

class StatusIndicator extends StatelessWidget {
  final String text;
  final StatusType type;
  final StatusSize size;
  final IconData? icon;
  final bool showIcon;

  const StatusIndicator({
    super.key,
    required this.text,
    required this.type,
    this.size = StatusSize.medium,
    this.icon,
    this.showIcon = true,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: _getPadding(),
      decoration: BoxDecoration(
        color: _getBackgroundColor(),
        borderRadius: BorderRadius.circular(_getBorderRadius()),
        border: Border.all(color: _getBorderColor(), width: 1),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (showIcon) ...[
            Icon(
              icon ?? _getDefaultIcon(),
              color: _getIconColor(),
              size: _getIconSize(),
            ),
            SizedBox(width: _getIconSpacing()),
          ],
          Text(
            text,
            style: TextStyle(
              color: _getTextColor(),
              fontSize: _getFontSize(),
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  EdgeInsetsGeometry _getPadding() {
    switch (size) {
      case StatusSize.small:
        return const EdgeInsets.symmetric(horizontal: 8, vertical: 4);
      case StatusSize.medium:
        return const EdgeInsets.symmetric(horizontal: 12, vertical: 6);
      case StatusSize.large:
        return const EdgeInsets.symmetric(horizontal: 16, vertical: 8);
    }
  }

  double _getBorderRadius() {
    switch (size) {
      case StatusSize.small:
        return 6;
      case StatusSize.medium:
        return 8;
      case StatusSize.large:
        return 10;
    }
  }

  double _getFontSize() {
    switch (size) {
      case StatusSize.small:
        return 12;
      case StatusSize.medium:
        return 14;
      case StatusSize.large:
        return 16;
    }
  }

  double _getIconSize() {
    switch (size) {
      case StatusSize.small:
        return 14;
      case StatusSize.medium:
        return 16;
      case StatusSize.large:
        return 18;
    }
  }

  double _getIconSpacing() {
    switch (size) {
      case StatusSize.small:
        return 4;
      case StatusSize.medium:
        return 6;
      case StatusSize.large:
        return 8;
    }
  }

  Color _getBackgroundColor() {
    switch (type) {
      case StatusType.success:
        return AppColors.buttonBackground; // Transparent background
      case StatusType.warning:
        return AppColors.buttonBackground; // Transparent background
      case StatusType.error:
        return AppColors.buttonBackground; // Transparent background
      case StatusType.info:
        return AppColors.buttonBackground; // Transparent background
      case StatusType.neutral:
        return AppColors.background; // Dark blue background
    }
  }

  Color _getBorderColor() {
    switch (type) {
      case StatusType.success:
        return AppColors.buttonText.withOpacity(0.3); // Red with opacity
      case StatusType.warning:
        return AppColors.buttonText.withOpacity(0.3); // Red with opacity
      case StatusType.error:
        return AppColors.buttonText.withOpacity(0.3); // Red with opacity
      case StatusType.info:
        return AppColors.buttonText.withOpacity(0.3); // Red with opacity
      case StatusType.neutral:
        return AppColors.textPrimary.withOpacity(0.3); // White with opacity
    }
  }

  Color _getTextColor() {
    switch (type) {
      case StatusType.success:
        return AppColors.textPrimary; // White
      case StatusType.warning:
        return AppColors.textPrimary; // White
      case StatusType.error:
        return AppColors.textPrimary; // White
      case StatusType.info:
        return AppColors.textPrimary; // White
      case StatusType.neutral:
        return AppColors.textPrimary; // White
    }
  }

  Color _getIconColor() {
    return _getTextColor();
  }

  IconData _getDefaultIcon() {
    switch (type) {
      case StatusType.success:
        return Icons.check_circle;
      case StatusType.warning:
        return Icons.warning;
      case StatusType.error:
        return Icons.error;
      case StatusType.info:
        return Icons.info;
      case StatusType.neutral:
        return Icons.circle;
    }
  }
}

// Convenience widgets for common status types
class SuccessIndicator extends StatelessWidget {
  final String text;
  final StatusSize size;
  final IconData? icon;
  final bool showIcon;

  const SuccessIndicator({
    super.key,
    required this.text,
    this.size = StatusSize.medium,
    this.icon,
    this.showIcon = true,
  });

  @override
  Widget build(BuildContext context) {
    return StatusIndicator(
      text: text,
      type: StatusType.success,
      size: size,
      icon: icon,
      showIcon: showIcon,
    );
  }
}

class WarningIndicator extends StatelessWidget {
  final String text;
  final StatusSize size;
  final IconData? icon;
  final bool showIcon;

  const WarningIndicator({
    super.key,
    required this.text,
    this.size = StatusSize.medium,
    this.icon,
    this.showIcon = true,
  });

  @override
  Widget build(BuildContext context) {
    return StatusIndicator(
      text: text,
      type: StatusType.warning,
      size: size,
      icon: icon,
      showIcon: showIcon,
    );
  }
}

class ErrorIndicator extends StatelessWidget {
  final String text;
  final StatusSize size;
  final IconData? icon;
  final bool showIcon;

  const ErrorIndicator({
    super.key,
    required this.text,
    this.size = StatusSize.medium,
    this.icon,
    this.showIcon = true,
  });

  @override
  Widget build(BuildContext context) {
    return StatusIndicator(
      text: text,
      type: StatusType.error,
      size: size,
      icon: icon,
      showIcon: showIcon,
    );
  }
}

class InfoIndicator extends StatelessWidget {
  final String text;
  final StatusSize size;
  final IconData? icon;
  final bool showIcon;

  const InfoIndicator({
    super.key,
    required this.text,
    this.size = StatusSize.medium,
    this.icon,
    this.showIcon = true,
  });

  @override
  Widget build(BuildContext context) {
    return StatusIndicator(
      text: text,
      type: StatusType.info,
      size: size,
      icon: icon,
      showIcon: showIcon,
    );
  }
}