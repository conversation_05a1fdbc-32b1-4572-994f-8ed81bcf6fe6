/// Résultat d'un téléchargement de rapport
class DownloadResult {
  final bool success;
  final bool isExcelFile;
  final String content;
  final String filename;
  final String? filePath;
  final String? errorMessage;

  DownloadResult({
    required this.success,
    required this.isExcelFile,
    required this.content,
    required this.filename,
    this.filePath,
    this.errorMessage,
  });

  /// Créer un résultat de succès pour un fichier Excel
  factory DownloadResult.excelSuccess({
    required String filename,
    required String filePath,
    required String content,
  }) {
    return DownloadResult(
      success: true,
      isExcelFile: true,
      filename: filename,
      filePath: filePath,
      content: content,
    );
  }

  /// Créer un résultat de succès pour un fichier CSV
  factory DownloadResult.csvSuccess({
    required String filename,
    required String content,
  }) {
    return DownloadResult(
      success: true,
      isExcelFile: false,
      filename: filename,
      content: content,
    );
  }

  /// Créer un résultat d'erreur
  factory DownloadResult.error({
    required String filename,
    required bool isExcelFile,
    required String errorMessage,
  }) {
    return DownloadResult(
      success: false,
      isExcelFile: isExcelFile,
      filename: filename,
      content: '',
      errorMessage: errorMessage,
    );
  }

  @override
  String toString() {
    return 'DownloadResult(success: $success, isExcelFile: $isExcelFile, filename: $filename, filePath: $filePath)';
  }
}