import 'package:flutter/material.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_constants.dart';
import '../../models/models.dart';
import '../../routes/app_routes.dart';

class SiteAssignmentsWidget extends StatelessWidget {
  final Site site;
  final VoidCallback? onAssignmentChanged;

  const SiteAssignmentsWidget({
    super.key,
    required this.site,
    this.onAssignmentChanged,
  });

  @override
  Widget build(BuildContext context) {
    final assignedUsers = site.users ?? [];

    return Card(
      color: AppColors.background,
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.people,
                  color: AppColors.textPrimary,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  'الموظفون المعينون',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppColors.textPrimary,
                  ),
                ),
                const Spacer(),
                TextButton.icon(
                  onPressed: () => _navigateToAssignment(context),
                  icon: const Icon(Icons.edit, size: 16, color: AppColors.textPrimary),
                  label: const Text('تعديل', style: TextStyle(color: AppColors.textPrimary)),
                  style: TextButton.styleFrom(
                    backgroundColor: AppColors.buttonBackground,
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            if (assignedUsers.isEmpty)
              _buildEmptyState(context)
            else
              _buildAssignedUsersList(context, assignedUsers),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding * 2),
      decoration: BoxDecoration(
        color: AppColors.background.withOpacity(0.5),
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        border: Border.all(
          color: AppColors.background,
          style: BorderStyle.solid,
        ),
      ),
      child: Column(
        children: [
          Icon(
            Icons.people_outline,
            size: 48,
            color: AppColors.textPrimary,
          ),
          const SizedBox(height: 12),
          Text(
            'لا يوجد موظفون معينون لهذا الموقع',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppColors.textPrimary,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton.icon(
            onPressed: () => _navigateToAssignment(context),
            icon: const Icon(Icons.add, color: AppColors.textPrimary),
            label: const Text('تعيين موظفين', style: TextStyle(color: AppColors.textPrimary)),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.buttonBackground,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAssignedUsersList(BuildContext context, List<User> users) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.symmetric(
            horizontal: AppConstants.defaultPadding,
            vertical: 8,
          ),
          decoration: BoxDecoration(
            color: AppColors.background.withOpacity(0.7),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            children: [
              Icon(
                Icons.info_outline,
                color: AppColors.textPrimary,
                size: 16,
              ),
              const SizedBox(width: 8),
              Text(
                'إجمالي الموظفين: ${users.length}',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: AppColors.textPrimary,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: AppConstants.defaultPadding),
        ListView.separated(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: users.length,
          separatorBuilder: (context, index) => const SizedBox(height: 8),
          itemBuilder: (context, index) {
            final user = users[index];
            return _buildUserCard(context, user);
          },
        ),
      ],
    );
  }

  Widget _buildUserCard(BuildContext context, User user) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppColors.background.withOpacity(0.8),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: AppColors.background,
          width: 1,
        ),
      ),
      child: Row(
        children: [
          CircleAvatar(
            backgroundColor: AppColors.background.withOpacity(0.6),
            radius: 20,
            child: Icon(
              Icons.person,
              color: AppColors.textPrimary,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  user.name,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: AppColors.textPrimary,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  user.email,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppColors.textPrimary,
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: AppColors.buttonBackground,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: AppColors.buttonText.withOpacity(0.3),
              ),
            ),
            child: Text(
              'معين',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: AppColors.textPrimary,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _navigateToAssignment(BuildContext context) {
    Navigator.pushNamed(
      context,
      AppRoutes.siteAssignment,
      arguments: {'site': site},
    ).then((result) {
      if (result == true && onAssignmentChanged != null) {
        onAssignmentChanged!();
      }
    });
  }
}