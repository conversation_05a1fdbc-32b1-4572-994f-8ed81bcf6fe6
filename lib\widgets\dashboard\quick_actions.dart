import 'package:flutter/material.dart';
import '../../constants/app_constants.dart';
import '../../providers/attendance_provider.dart';
import '../../routes/app_routes.dart';
import '../common/custom_button.dart';

class QuickActions extends StatelessWidget {
  final AttendanceProvider attendanceProvider;

  const QuickActions({super.key, required this.attendanceProvider});

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'إجراءات سريعة',
              style: Theme.of(
                context,
              ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: CustomButton(
                    text: 'تسجيل الحضور',
                    onPressed: () {
                      Navigator.pushNamed(context, AppRoutes.attendanceScreen);
                    },
                    icon: Icons.access_time,
                    type: ButtonType.success,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: <PERSON><PERSON><PERSON><PERSON>(
                    text: 'الملف الشخصي',
                    onPressed: () {
                      Navigator.pushNamed(context, AppRoutes.profileScreen);
                    },
                    icon: Icons.person,
                    type: ButtonType.secondary,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
