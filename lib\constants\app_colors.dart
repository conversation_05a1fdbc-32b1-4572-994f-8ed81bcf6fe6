import 'package:flutter/material.dart';

class AppColors {
  // Background Colors - Based on image background
  static const Color background = Color(0xFF1E2A44); // Dark blue background from image

  // Text and Icon Colors - White for all text and icons
  static const Color textPrimary = Color(0xFFFFFFFF);
  static const Color textSecondary = Color(0xFFFFFFFF);
  static const Color textHint = Color(0xFFFFFFFF);
  static const Color iconColor = Color(0xFFFFFFFF);

  // Button Colors - Red from logo for delete/modify buttons, transparent background
  static const Color buttonText = Color(0xFFEF5350); // Red from logo
  static const Color buttonBackground = Color(0x801E2A44); // Transparent version of background

  // Theme-specific color getters
  static Color getTextColor(bool isDark) {
    return textPrimary;
  }

  static Color getBackgroundColor(bool isDark) {
    return background;
  }

  static Color getSurfaceColor(bool isDark) {
    return background;
  }

  static Color getBorderColor(bool isDark) {
    return background;
  }
}