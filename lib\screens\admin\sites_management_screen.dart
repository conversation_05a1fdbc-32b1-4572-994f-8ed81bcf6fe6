import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_constants.dart';
import '../../providers/providers.dart';
import '../../models/models.dart';
import '../../widgets/common/custom_error_widget.dart';
import '../../routes/app_routes.dart';

class SitesManagementScreen extends StatefulWidget {
  const SitesManagementScreen({super.key});

  @override
  State<SitesManagementScreen> createState() => _SitesManagementScreenState();
}

class _SitesManagementScreenState extends State<SitesManagementScreen> {
  bool _isMapView = false;
  GoogleMapController? _mapController;
  final Set<Marker> _markers = {};

  // Coordonnées par défaut pour l'Algérie (Alger)
  static const LatLng _algerCenter = LatLng(36.7538, 3.0588);

  // Villes principales d'Algérie avec leurs coordonnées
  final Map<String, LatLng> _algerianCities = {
    'الجزائر العاصمة': LatLng(36.7538, 3.0588),
    'وهران': LatLng(35.6911, -0.6417),
    'قسنطينة': LatLng(36.3650, 6.6147),
    'عنابة': LatLng(36.9000, 7.7667),
    'باتنة': LatLng(35.5667, 6.1667),
    'سطيف': LatLng(36.1833, 5.4167),
    'سيدي بلعباس': LatLng(35.2167, -0.6333),
    'بسكرة': LatLng(34.8500, 5.7333),
    'تبسة': LatLng(35.4000, 8.1167),
    'ورقلة': LatLng(31.9500, 5.3333),
  };

  @override
  void initState() {
    super.initState();
    _loadSites();
  }

  void _loadSites() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<SitesProvider>().refreshSites();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة المواقع'),
        backgroundColor: AppColors.background,
        foregroundColor: AppColors.textPrimary,
        actions: [
          if (_isMapView) ...[
            PopupMenuButton<String>(
              icon: Icon(Icons.location_city, color: AppColors.textPrimary),
              tooltip: 'الانتقال إلى مدينة',
              onSelected: _goToCity,
              itemBuilder: (context) => _algerianCities.keys
                  .map((city) => PopupMenuItem(value: city, child: Text(city, style: TextStyle(color: AppColors.textPrimary))))
                  .toList(),
            ),
          ],
        ],
      ),
      body: Consumer<SitesProvider>(
        builder: (context, provider, child) {
          if (provider.isLoading) {
            return const Center(child: CircularProgressIndicator(color: AppColors.textPrimary));
          }

          if (provider.state == SitesState.error) {
            return CustomErrorWidget(
              message: provider.errorMessage ?? 'حدث خطأ في تحميل البيانات',
              onRetry: _loadSites,
            );
          }

          if (provider.sites.isEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.location_off,
                    size: 100,
                    color: AppColors.textPrimary,
                  ),
                  const SizedBox(height: 20),
                  Text(
                    'لا توجد مواقع',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(color: AppColors.textPrimary),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'ابدأ بإضافة موقع جديد',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(color: AppColors.textPrimary),
                  ),
                  const SizedBox(height: 24),
                  ElevatedButton.icon(
                    onPressed: () {
                      Navigator.pushNamed(context, AppRoutes.addSite);
                    },
                    icon: Icon(Icons.add, color: AppColors.textPrimary),
                    label: const Text('إضافة موقع', style: TextStyle(color: AppColors.textPrimary)),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.buttonBackground,
                    ),
                  ),
                  const SizedBox(height: 16),
                  TextButton.icon(
                    onPressed: () {
                      debugPrint('Manual refresh requested');
                      _loadSites();
                    },
                    icon: Icon(Icons.refresh, color: AppColors.textPrimary),
                    label: const Text('إعادة تحميل', style: TextStyle(color: AppColors.textPrimary)),
                  ),
                ],
              ),
            );
          }

          return _isMapView
              ? _buildMapView(provider.sites)
              : _buildListView(provider.sites);
        },
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => Navigator.pushNamed(context, AppRoutes.addSite),
        backgroundColor: AppColors.buttonBackground,
        child: Icon(Icons.add, color: AppColors.textPrimary),
      ),
    );
  }

  Widget _buildListView(List<Site> sites) {
    debugPrint('SitesManagementScreen: Building list view with ${sites.length} sites');
    return Column(
      children: [
        _buildStatsHeader(sites),
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            itemCount: sites.length,
            itemBuilder: (context, index) {
              final site = sites[index];
              debugPrint('SitesManagementScreen: Building card for site: ${site.name} (${site.id})');
              return _buildSiteCard(site);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildStatsHeader(List<Site> sites) {
    final sitesWithCoordinates = sites.where((s) => s.latitude != 0 && s.longitude != 0).length;
    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      decoration: BoxDecoration(
        color: AppColors.background,
        boxShadow: [
          BoxShadow(
            color: AppColors.background.withOpacity(0.1),
            blurRadius: 5,
            offset: const Offset(0, 2),
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: color,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(color: AppColors.textPrimary),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildSiteCard(Site site) {
    return Card(
      margin: const EdgeInsets.only(bottom: AppConstants.defaultPadding),
      color: AppColors.background,
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: AppColors.buttonBackground,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    Icons.location_on,
                    color: AppColors.textPrimary,
                    size: 24,
                  ),
                ),
                const SizedBox(width: AppConstants.defaultPadding),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        site.name,
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold, color: AppColors.textPrimary),
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 4),
                    ],
                  ),
                ),
                PopupMenuButton<String>(
                  onSelected: (value) => _handleSiteAction(value, site),
                  itemBuilder: (context) => [
                    PopupMenuItem(
                      value: 'view',
                      child: Row(
                        children: [
                          Icon(Icons.visibility, color: AppColors.textPrimary),
                          SizedBox(width: 8),
                          Text('عرض التفاصيل', style: TextStyle(color: AppColors.textPrimary)),
                        ],
                      ),
                    ),
                    PopupMenuItem(
                      value: 'edit',
                      child: Row(
                        children: [
                          Icon(Icons.edit, color: AppColors.textPrimary),
                          SizedBox(width: 8),
                          Text('تعديل', style: TextStyle(color: AppColors.textPrimary)),
                        ],
                      ),
                    ),
                    PopupMenuItem(
                      value: 'delete',
                      child: Row(
                        children: [
                          Icon(Icons.delete, color: AppColors.buttonText),
                          SizedBox(width: 8),
                          Text('حذف', style: TextStyle(color: AppColors.textPrimary)),
                        ],
                      ),
                    ),
                  ],
                  color: AppColors.background,
                ),
              ],
            ),
           
          ],
      ),),
    );
  }

  Widget _buildInfoItem({
    required IconData icon,
    required String label,
    required String value,
  }) {
    return Row(
      children: [
        Icon(icon, size: 16, color: AppColors.textPrimary),
        const SizedBox(width: 8),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              label,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(color: AppColors.textPrimary),
            ),
            Text(
              value,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w600, color: AppColors.textPrimary),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildAssignedEmployeesSection(List<User> users) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppColors.background,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: AppColors.background.withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 8),
          Wrap(
            spacing: 8,
            runSpacing: 4,
            children: users.take(3).map((user) => Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: AppColors.background,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: AppColors.buttonText.withOpacity(0.2)),
              ),
              child: Text(
                user.name,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(color: AppColors.textPrimary, fontWeight: FontWeight.w500),
              ),
            )).toList()
            ..addAll(users.length > 3 ? [
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: AppColors.background,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: AppColors.buttonText.withOpacity(0.2)),
                ),
                child: Text(
                  '+${users.length - 3}',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(color: AppColors.buttonText, fontWeight: FontWeight.w500),
                ),
              ),
            ] : []),
          ),
        ],
      ),
    );
  }

  Widget _buildMapView(List<Site> sites) {
    return GoogleMap(
      onMapCreated: (GoogleMapController controller) {
        _mapController = controller;
        _updateMapMarkers();
      },
      initialCameraPosition: const CameraPosition(
        target: _algerCenter,
        zoom: 10,
      ),
      markers: _markers,
      onTap: (LatLng position) {
        // TODO: Add new site at tapped position
      },
    );
  }

  void _updateMapMarkers() {
    final sites = context.read<SitesProvider>().sites;
    _markers.clear();

    for (final site in sites) {
      if (site.latitude != 0 && site.longitude != 0) {
        _markers.add(
          Marker(
            markerId: MarkerId(site.id.toString()),
            position: LatLng(site.latitude, site.longitude),
            infoWindow: InfoWindow(
              title: site.name,
              snippet: 'ID: ${site.id}',
              onTap: () => _handleSiteAction('view', site),
            ),
          ),
        );
      }
    }

    if (mounted) {
      setState(() {});
    }
  }

  void _handleSiteAction(String action, Site site) {
    switch (action) {
      case 'view':
        Navigator.pushNamed(context, AppRoutes.siteDetails, arguments: {'siteId': site.id});
        break;
      case 'assign':
        Navigator.pushNamed(context, AppRoutes.siteAssignment, arguments: {'site': site}).then((result) {
          if (result == true) {
            _loadSites();
          }
        });
        break;
      case 'edit':
        Navigator.pushNamed(context, AppRoutes.editSite, arguments: {'site': site});
        break;
      case 'delete':
        _showDeleteDialog(site);
        break;
    }
  }

  void _showOnMap(Site site) {
    setState(() {
      _isMapView = true;
    });

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _mapController?.animateCamera(CameraUpdate.newLatLngZoom(LatLng(site.latitude, site.longitude), 15));
    });
  }

  void _showDeleteDialog(Site site) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppColors.background,
        title: Text('تأكيد الحذف', style: TextStyle(color: AppColors.textPrimary)),
        content: Text('هل أنت متأكد من حذف الموقع "${site.name}"؟', style: TextStyle(color: AppColors.textPrimary)),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('إلغاء', style: TextStyle(color: AppColors.buttonText)),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _deleteSite(site);
            },
            style: ElevatedButton.styleFrom(backgroundColor: AppColors.buttonText),
            child: Text('حذف', style: TextStyle(color: AppColors.textPrimary)),
          ),
        ],
      ),
    );
  }

  void _goToCity(String cityName) {
    final location = _algerianCities[cityName];
    if (location != null && _mapController != null) {
      _mapController!.animateCamera(CameraUpdate.newLatLngZoom(location, 12));
    }
  }

  void _deleteSite(Site site) async {
    try {
      await context.read<SitesProvider>().deleteSite(site.id);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text('تم حذف الموقع بنجاح', style: TextStyle(color: AppColors.textPrimary))));
        _updateMapMarkers();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text('فشل في حذف الموقع: $e', style: TextStyle(color: AppColors.textPrimary))));
      }
    }
  }
}