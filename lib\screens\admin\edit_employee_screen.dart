import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_constants.dart';
import '../../providers/providers.dart';
import '../../models/models.dart';

class EditEmployeeScreen extends StatefulWidget {
  final User employee;

  const EditEmployeeScreen({super.key, required this.employee});

  @override
  State<EditEmployeeScreen> createState() => _EditEmployeeScreenState();
}

class _EditEmployeeScreenState extends State<EditEmployeeScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _emailController = TextEditingController();
  final _phoneController = TextEditingController();
  final _nationalIdController = TextEditingController();
  final _employeeIdController = TextEditingController();
  final _departmentController = TextEditingController();
  final _positionController = TextEditingController();
  final _salaryController = TextEditingController();

  String _selectedRole = 'employee';
  int? _selectedSiteId;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadEmployeeData();
  }

  void _loadEmployeeData() {
    final employee = widget.employee;
    _nameController.text = employee.name;
    _emailController.text = employee.email;
    _phoneController.text = employee.phone ?? '';
    _nationalIdController.text = employee.nationalId ?? '';
    _employeeIdController.text = employee.employeeId ?? '';
    _departmentController.text = employee.department ?? '';
    _positionController.text = employee.position ?? '';
    _salaryController.text = employee.salary?.toString() ?? '';
    _selectedRole = employee.role;
    _selectedSiteId = employee.defaultSiteId;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('تعديل ${widget.employee.name}', style: TextStyle(color: AppColors.textPrimary)),
        backgroundColor: AppColors.buttonBackground,
        foregroundColor: AppColors.textPrimary,
        actions: [
          IconButton(
            icon: const Icon(Icons.save, color: AppColors.textPrimary),
            onPressed: _isLoading ? null : _saveEmployee,
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildEmployeeHeader(),
              const SizedBox(height: AppConstants.largePadding),
              _buildPersonalInfoSection(),
              const SizedBox(height: AppConstants.defaultPadding),
              _buildAccountInfoSection(),
              const SizedBox(height: AppConstants.largePadding),
              _buildActionButtons(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEmployeeHeader() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [AppColors.buttonBackground, AppColors.background],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
      ),
      child: Row(
        children: [
          CircleAvatar(
            radius: 30,
            backgroundColor: AppColors.textPrimary.withOpacity(0.2),
            child: Text(
              widget.employee.name.isNotEmpty
                  ? widget.employee.name[0].toUpperCase()
                  : 'U',
              style: const TextStyle(
                color: AppColors.textPrimary,
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          const SizedBox(width: AppConstants.defaultPadding),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'تعديل بيانات الموظف',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    color: AppColors.textPrimary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPersonalInfoSection() {
    return Card(
      color: AppColors.background,
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'المعلومات الشخصية',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppColors.textPrimary,
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            TextFormField(
              controller: _nameController,
              decoration: const InputDecoration(
                labelText: '',
                prefixIcon: Icon(Icons.person, color: AppColors.textPrimary),
                border: OutlineInputBorder(),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'الاسم مطلوب';
                }
                if (value.length < 2) {
                  return 'الاسم يجب أن يكون أكثر من حرفين';
                }
                return null;
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAccountInfoSection() {
    return Card(
      color: AppColors.background,
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'معلومات الحساب',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppColors.textPrimary,
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            TextFormField(
              controller: _emailController,
              decoration: const InputDecoration(
                labelText: '',
                prefixIcon: Icon(Icons.email, color: AppColors.textPrimary),
                border: OutlineInputBorder(),
              ),
              keyboardType: TextInputType.emailAddress,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'البريد الإلكتروني مطلوب';
                }
                if (!RegExp(
                  r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$',
                ).hasMatch(value)) {
                  return 'البريد الإلكتروني غير صحيح';
                }
                return null;
              },
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            DropdownButtonFormField<String>(
              initialValue: _selectedRole,
              decoration: const InputDecoration(
                labelText: '',
                prefixIcon: Icon(Icons.admin_panel_settings, color: AppColors.textPrimary),
                border: OutlineInputBorder(),
              ),
              items: const [
                DropdownMenuItem(value: 'employee', child: Text('موظف', style: TextStyle(color: AppColors.textPrimary))),
                DropdownMenuItem(value: 'supervisor', child: Text('مشرف', style: TextStyle(color: AppColors.textPrimary))),
                DropdownMenuItem(value: 'admin', child: Text('مدير', style: TextStyle(color: AppColors.textPrimary))),
              ],
              onChanged: (value) {
                setState(() {
                  _selectedRole = value!;
                });
              },
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'الدور مطلوب';
                }
                return null;
              },
            ),
          ],
        ),
      ),
    );
  }

  

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton(
            onPressed: _isLoading ? null : () => Navigator.pop(context),
            style: OutlinedButton.styleFrom(
              foregroundColor: AppColors.buttonText,
              side: const BorderSide(color: AppColors.buttonText),
            ),
            child: const Text('إلغاء', style: TextStyle(color: AppColors.textPrimary)),
          ),
        ),
        const SizedBox(width: AppConstants.defaultPadding),
        Expanded(
          child: ElevatedButton(
            onPressed: _isLoading ? null : _saveEmployee,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.buttonBackground,
              foregroundColor: AppColors.buttonText,
            ),
            child: _isLoading
                ? const SizedBox(
                    height: 20,
                    width: 20,
                    child: CircularProgressIndicator(strokeWidth: 2, color: AppColors.textPrimary),
                  )
                : const Text('حفظ التغييرات', style: TextStyle(color: AppColors.textPrimary)),
          ),
        ),
      ],
    );
  }

  void _saveEmployee() async {
    if (_formKey.currentState?.validate() ?? false) {
      setState(() {
        _isLoading = true;
      });

      try {
        await context.read<EmployeesProvider>().updateEmployee(
          id: widget.employee.id,
          name: _nameController.text,
          email: _emailController.text,
          role: _selectedRole,
        );

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم تحديث بيانات الموظف بنجاح', style: TextStyle(color: AppColors.background)),
              backgroundColor:  Color.fromARGB(255, 80, 239, 114),
            ),
          );
          Navigator.pop(context);
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('فشل في تحديث بيانات الموظف: $e', style: TextStyle(color: AppColors.background)),
              backgroundColor: AppColors.buttonText,
            ),
          );
        }
      } finally {
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }
      }
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _nationalIdController.dispose();
    _employeeIdController.dispose();
    _departmentController.dispose();
    _positionController.dispose();
    _salaryController.dispose();
    super.dispose();
  }
}