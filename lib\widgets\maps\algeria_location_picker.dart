import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:geolocator/geolocator.dart';
import 'package:geocoding/geocoding.dart';
import '../../constants/app_colors.dart';
import 'simple_place_search.dart';


class AlgeriaLocationPicker extends StatefulWidget {
  final LatLng? initialLocation;
  final double initialRadius;
  final Function(LatLng location, String address) onLocationSelected;
  final Function(double radius) onRadiusChanged;

  const AlgeriaLocationPicker({
    super.key,
    this.initialLocation,
    this.initialRadius = 100,
    required this.onLocationSelected,
    required this.onRadiusChanged,
  });

  @override
  State<AlgeriaLocationPicker> createState() => _AlgeriaLocationPickerState();
}

class _AlgeriaLocationPickerState extends State<AlgeriaLocationPicker> {
  GoogleMapController? _mapController;
  LatLng? _selectedLocation;
  String? _selectedAddress;
  double _radius = 100;
  bool _isLoadingLocation = false;


  // Coordonnées de l'Algérie
  static const LatLng _algerCenter = LatLng(36.7538, 3.0588);
  static const LatLng _algerNorthEast = LatLng(37.2962, 11.9795);
  static const LatLng _algerSouthWest = LatLng(18.9681, -8.6676);

  // Villes principales d'Algérie avec leurs coordonnées
  final Map<String, LatLng> _algerianCities = {
    'الجزائر العاصمة': LatLng(36.7538, 3.0588),
    'وهران': LatLng(35.6911, -0.6417),
    'قسنطينة': LatLng(36.3650, 6.6147),
    'عنابة': LatLng(36.9000, 7.7667),
    'باتنة': LatLng(35.5667, 6.1667),
    'سطيف': LatLng(36.1833, 5.4167),
    'سيدي بلعباس': LatLng(35.2167, -0.6333),
    'بسكرة': LatLng(34.8500, 5.7333),
    'تبسة': LatLng(35.4000, 8.1167),
    'ورقلة': LatLng(31.9500, 5.3333),
    'بجاية': LatLng(36.7525, 5.0767),
    'تيزي وزو': LatLng(36.7167, 4.0500),
    'المسيلة': LatLng(35.7056, 4.5431),
    'برج بوعريريج': LatLng(36.0731, 4.7608),
    'الشلف': LatLng(36.1667, 1.3333),
    'جيجل': LatLng(36.8065, 5.7661),
    'سكيكدة': LatLng(36.8706, 6.9094),
    'بشار': LatLng(31.6167, -2.2167),
    'أدرار': LatLng(27.8667, -0.2833),
    'تمنراست': LatLng(22.7833, 5.5167),
  };

  @override
  void initState() {
    super.initState();
    _selectedLocation = widget.initialLocation;
    _radius = widget.initialRadius;
    if (_selectedLocation != null) {
      _getAddressFromCoordinates(_selectedLocation!);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildHeader(),
        const SizedBox(height: 16),
        _buildPlaceSearch(),
        const SizedBox(height: 16),
        _buildCitySelector(),
        const SizedBox(height: 16),
        _buildMap(),
        const SizedBox(height: 16),
        _buildRadiusSelector(),
        if (_selectedLocation != null) ...[
          const SizedBox(height: 16),
          _buildLocationInfo(),
        ],
      ],
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        Icon(
          Icons.location_on,
          color: AppColors.background,
          size: 24,
        ),
        const SizedBox(width: 8),
        Text(
          'اختيار موقع العمل في الجزائر',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: AppColors.background,
          ),
        ),
        const Spacer(),
        IconButton(
          onPressed: _isLoadingLocation ? null : _getCurrentLocation,
          icon: _isLoadingLocation
              ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : const Icon(Icons.my_location),
          tooltip: 'موقعي الحالي',
          style: IconButton.styleFrom(
            backgroundColor: AppColors.background.withValues(alpha: 0.1),
            foregroundColor: AppColors.background,
          ),
        ),
      ],
    );
  }

  Widget _buildPlaceSearch() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.buttonBackground,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.textSecondary),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.search,
                color: AppColors.background,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'البحث عن أي مكان في الجزائر:',
                style: TextStyle(
                  fontWeight: FontWeight.w600,
                  color: AppColors.textPrimary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          SimplePlaceSearch(
            onPlaceSelected: (location, placeName, address) {
              setState(() {
                _selectedLocation = location;
                _selectedAddress = '$placeName - $address';
              });

              if (_mapController != null) {
                _mapController!.animateCamera(
                  CameraUpdate.newLatLngZoom(location, 16),
                );
              }

              widget.onLocationSelected(location, _selectedAddress ?? '');
            },
          ),
        ],
      ),
    );
  }

  Widget _buildCitySelector() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.buttonBackground,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.textSecondary),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.location_city,
                color: AppColors.background,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'أو اختر مدينة للانتقال السريع:',
                style: TextStyle(
                  fontWeight: FontWeight.w600,
                  color: AppColors.textPrimary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: _algerianCities.keys.take(10).map((city) {
              return ActionChip(
                label: Text(city),
                onPressed: () => _goToCity(city),
                backgroundColor: AppColors.background.withValues(alpha: 0.1),
                labelStyle: TextStyle(
                  color: AppColors.background,
                  fontSize: 12,
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildMap() {
    return Container(
      height: 400,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.textSecondary),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12),
        child: GoogleMap(
          initialCameraPosition: CameraPosition(
            target: _selectedLocation ?? _algerCenter,
            zoom: _selectedLocation != null ? 14 : 6,
          ),
          onMapCreated: (GoogleMapController controller) {
            _mapController = controller;
            _fitMapToAlgeria();
          },
          onTap: _onMapTapped,
          markers: _selectedLocation != null
              ? {
                  Marker(
                    markerId: const MarkerId('selected_location'),
                    position: _selectedLocation!,
                    infoWindow: InfoWindow(
                      title: 'موقع العمل المحدد',
                      snippet: _selectedAddress ?? 'الموقع المحدد',
                    ),
                    icon: BitmapDescriptor.defaultMarkerWithHue(
                      BitmapDescriptor.hueBlue,
                    ),
                  ),
                }
              : {},
          circles: _selectedLocation != null
              ? {
                  Circle(
                    circleId: const CircleId('work_area'),
                    center: _selectedLocation!,
                    radius: _radius,
                    fillColor: AppColors.background.withValues(alpha: 0.2),
                    strokeColor: AppColors.background,
                    strokeWidth: 2,
                  ),
                }
              : {},
          mapType: MapType.normal,
          myLocationEnabled: true,
          myLocationButtonEnabled: false,
          zoomControlsEnabled: true,
          mapToolbarEnabled: false,
          compassEnabled: true,
          rotateGesturesEnabled: true,
          scrollGesturesEnabled: true,
          tiltGesturesEnabled: true,
          zoomGesturesEnabled: true,
          minMaxZoomPreference: const MinMaxZoomPreference(5, 20),
        ),
      ),
    );
  }

  Widget _buildRadiusSelector() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.buttonBackground,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.textSecondary),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.radio_button_unchecked,
                color: AppColors.background,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'نطاق العمل: ${_radius.toInt()} متر',
                style: TextStyle(
                  fontWeight: FontWeight.w600,
                  color: AppColors.textPrimary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Slider(
            value: _radius,
            min: 50,
            max: 1000,
            divisions: 19,
            label: '${_radius.toInt()} متر',
            onChanged: (value) {
              setState(() {
                _radius = value;
              });
              widget.onRadiusChanged(_radius);
            },
            activeColor: AppColors.background,
            inactiveColor: AppColors.background.withValues(alpha: 0.3),
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text('50 متر', style: TextStyle(color: AppColors.textSecondary, fontSize: 12)),
              Text('1000 متر', style: TextStyle(color: AppColors.textSecondary, fontSize: 12)),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildLocationInfo() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.textPrimary.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.textPrimary.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.check_circle,
                color: AppColors.textPrimary,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'تم تحديد الموقع بنجاح',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: AppColors.textPrimary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          _buildInfoRow('العنوان', _selectedAddress ?? 'جاري تحديد العنوان...'),
          const SizedBox(height: 8),
          _buildInfoRow('خط العرض', _selectedLocation!.latitude.toStringAsFixed(6)),
          const SizedBox(height: 8),
          _buildInfoRow('خط الطول', _selectedLocation!.longitude.toStringAsFixed(6)),
          const SizedBox(height: 8),
          _buildInfoRow('نطاق العمل', '${_radius.toInt()} متر'),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 80,
          child: Text(
            '$label:',
            style: TextStyle(
              fontWeight: FontWeight.w600,
              color: AppColors.textPrimary,
              fontSize: 12,
            ),
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: TextStyle(
              color: AppColors.textPrimary,
              fontSize: 12,
            ),
          ),
        ),
      ],
    );
  }

  // Méthodes pour les fonctionnalités de la carte
  void _fitMapToAlgeria() {
    if (_mapController != null) {
      _mapController!.animateCamera(
        CameraUpdate.newLatLngBounds(
          LatLngBounds(
            southwest: _algerSouthWest,
            northeast: _algerNorthEast,
          ),
          100.0,
        ),
      );
    }
  }

  void _onMapTapped(LatLng location) {
    setState(() {
      _selectedLocation = location;
    });
    _getAddressFromCoordinates(location);
    widget.onLocationSelected(location, _selectedAddress ?? '');
  }

  Future<void> _getCurrentLocation() async {
    setState(() {
      _isLoadingLocation = true;
    });

    try {
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          _showSnackBar('تم رفض إذن الموقع', isError: true);
          return;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        _showSnackBar('إذن الموقع مرفوض نهائياً. يرجى تفعيله من الإعدادات', isError: true);
        return;
      }

      Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );

      final location = LatLng(position.latitude, position.longitude);

      setState(() {
        _selectedLocation = location;
      });

      if (_mapController != null) {
        await _mapController!.animateCamera(
          CameraUpdate.newLatLngZoom(location, 16),
        );
      }

      await _getAddressFromCoordinates(location);
      widget.onLocationSelected(location, _selectedAddress ?? '');
      _showSnackBar('تم تحديد موقعك الحالي بنجاح');
    } catch (e) {
      _showSnackBar('خطأ في الحصول على الموقع: ${e.toString()}', isError: true);
    } finally {
      setState(() {
        _isLoadingLocation = false;
      });
    }
  }

  Future<void> _getAddressFromCoordinates(LatLng location) async {
    try {
      List<Placemark> placemarks = await placemarkFromCoordinates(
        location.latitude,
        location.longitude,
      );

      if (placemarks.isNotEmpty) {
        final placemark = placemarks.first;
        final address = [
          placemark.street,
          placemark.locality,
          placemark.administrativeArea,
          'الجزائر',
        ].where((element) => element != null && element.isNotEmpty).join(', ');

        setState(() {
          _selectedAddress = address;
        });
      }
    } catch (e) {
      debugPrint('خطأ في الحصول على العنوان: $e');
      setState(() {
        _selectedAddress = 'عنوان غير محدد';
      });
    }
  }

  Future<void> _goToCity(String cityName) async {
    final location = _algerianCities[cityName];
    if (location != null && _mapController != null) {
      setState(() {
        _selectedLocation = location;
      });

      await _mapController!.animateCamera(
        CameraUpdate.newLatLngZoom(location, 12),
      );

      await _getAddressFromCoordinates(location);
      widget.onLocationSelected(location, _selectedAddress ?? '');
    }
  }

  void _showSnackBar(String message, {bool isError = false}) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: isError ? AppColors.textPrimary : AppColors.textPrimary,
          behavior: SnackBarBehavior.floating,
        ),
      );
    }
  }
}
