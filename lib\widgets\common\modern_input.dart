import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../constants/app_colors.dart';

enum InputVariant { outlined, filled, underlined }

enum InputSize { small, medium, large }

class ModernInput extends StatefulWidget {
  final TextEditingController? controller;
  final String? labelText;
  final String? hintText;
  final String? helperText;
  final String? errorText;
  final IconData? prefixIcon;
  final Widget? suffixIcon;
  final bool obscureText;
  final TextInputType keyboardType;
  final TextInputAction textInputAction;
  final int maxLines;
  final int? maxLength;
  final bool enabled;
  final bool readOnly;
  final bool autofocus;
  final String? Function(String?)? validator;
  final void Function(String)? onChanged;
  final void Function(String)? onFieldSubmitted;
  final VoidCallback? onTap;
  final List<TextInputFormatter>? inputFormatters;
  final EdgeInsetsGeometry? contentPadding;
  final TextDirection? textDirection;
  final InputVariant variant;
  final InputSize size;

  const ModernInput({
    super.key,
    this.controller,
    this.labelText,
    this.hintText,
    this.helperText,
    this.errorText,
    this.prefixIcon,
    this.suffixIcon,
    this.obscureText = false,
    this.keyboardType = TextInputType.text,
    this.textInputAction = TextInputAction.done,
    this.maxLines = 1,
    this.maxLength,
    this.enabled = true,
    this.readOnly = false,
    this.autofocus = false,
    this.validator,
    this.onChanged,
    this.onFieldSubmitted,
    this.onTap,
    this.inputFormatters,
    this.contentPadding,
    this.textDirection,
    this.variant = InputVariant.outlined,
    this.size = InputSize.medium,
  });

  @override
  State<ModernInput> createState() => _ModernInputState();
}

class _ModernInputState extends State<ModernInput> {
  late FocusNode _focusNode;
  bool _isFocused = false;

  @override
  void initState() {
    super.initState();
    _focusNode = FocusNode();
    _focusNode.addListener(_onFocusChange);
  }

  @override
  void dispose() {
    _focusNode.removeListener(_onFocusChange);
    _focusNode.dispose();
    super.dispose();
  }

  void _onFocusChange() {
    setState(() {
      _isFocused = _focusNode.hasFocus;
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        if (widget.labelText != null) ...[
          Text(
            widget.labelText!,
            style: theme.textTheme.labelMedium?.copyWith(
              color: _isFocused ? AppColors.buttonText : AppColors.textSecondary,
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: _getLabelSpacing()),
        ],
        TextFormField(
          controller: widget.controller,
          focusNode: _focusNode,
          obscureText: widget.obscureText,
          keyboardType: widget.keyboardType,
          textInputAction: widget.textInputAction,
          maxLines: widget.maxLines,
          maxLength: widget.maxLength,
          enabled: widget.enabled,
          readOnly: widget.readOnly,
          autofocus: widget.autofocus,
          validator: widget.validator,
          onChanged: widget.onChanged,
          onFieldSubmitted: widget.onFieldSubmitted,
          onTap: widget.onTap,
          inputFormatters: widget.inputFormatters,
          textDirection: widget.textDirection ?? TextDirection.rtl,
          style: theme.textTheme.bodyLarge?.copyWith(
            color: widget.enabled ? AppColors.textPrimary : AppColors.textHint,
            fontSize: _getFontSize(),
          ),
          decoration: InputDecoration(
            hintText: widget.hintText,
            helperText: widget.helperText,
            errorText: widget.errorText,
            prefixIcon: widget.prefixIcon != null
                ? Icon(
                    widget.prefixIcon,
                    color: _isFocused ? AppColors.buttonText : AppColors.textSecondary,
                    size: _getIconSize(),
                  )
                : null,
            suffixIcon: widget.suffixIcon,
            filled: _shouldFill(),
            fillColor: _getFillColor(),
            border: _getBorder(),
            enabledBorder: _getEnabledBorder(),
            focusedBorder: _getFocusedBorder(),
            errorBorder: _getErrorBorder(),
            focusedErrorBorder: _getFocusedErrorBorder(),
            disabledBorder: _getDisabledBorder(),
            contentPadding: widget.contentPadding ?? _getContentPadding(),
            hintStyle: theme.textTheme.bodyLarge?.copyWith(
              color: AppColors.textHint,
              fontSize: _getFontSize(),
            ),
            helperStyle: theme.textTheme.bodySmall?.copyWith(
              color: AppColors.textSecondary,
            ),
            errorStyle: theme.textTheme.bodySmall?.copyWith(
              color: AppColors.buttonText, // Changed to red for error
            ),
          ),
        ),
      ],
    );
  }

  double _getLabelSpacing() {
    switch (widget.size) {
      case InputSize.small:
        return 4;
      case InputSize.medium:
        return 6;
      case InputSize.large:
        return 8;
    }
  }

  double _getFontSize() {
    switch (widget.size) {
      case InputSize.small:
        return 14;
      case InputSize.medium:
        return 16;
      case InputSize.large:
        return 18;
    }
  }

  double _getIconSize() {
    switch (widget.size) {
      case InputSize.small:
        return 20;
      case InputSize.medium:
        return 24;
      case InputSize.large:
        return 28;
    }
  }

  EdgeInsetsGeometry _getContentPadding() {
    switch (widget.size) {
      case InputSize.small:
        return const EdgeInsets.symmetric(horizontal: 12, vertical: 8);
      case InputSize.medium:
        return const EdgeInsets.symmetric(horizontal: 16, vertical: 12);
      case InputSize.large:
        return const EdgeInsets.symmetric(horizontal: 20, vertical: 16);
    }
  }

  bool _shouldFill() {
    return widget.variant == InputVariant.filled;
  }

  Color _getFillColor() {
    if (!widget.enabled) {
      return AppColors.background.withOpacity(0.5);
    }
    return _isFocused
        ? AppColors.background.withOpacity(0.1)
        : AppColors.background.withOpacity(0.2);
  }

  InputBorder _getBorder() {
    switch (widget.variant) {
      case InputVariant.outlined:
        return OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(
            color: AppColors.textHint.withOpacity(0.3),
            width: 1,
          ),
        );
      case InputVariant.filled:
        return OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide.none,
        );
      case InputVariant.underlined:
        return const UnderlineInputBorder(
          borderSide: BorderSide(color: AppColors.textHint, width: 1),
        );
    }
  }

  InputBorder _getEnabledBorder() {
    return _getBorder();
  }

  InputBorder _getFocusedBorder() {
    switch (widget.variant) {
      case InputVariant.outlined:
        return OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: AppColors.buttonText, width: 2),
        );
      case InputVariant.filled:
        return OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: AppColors.buttonText, width: 2),
        );
      case InputVariant.underlined:
        return const UnderlineInputBorder(
          borderSide: BorderSide(color: AppColors.buttonText, width: 2),
        );
    }
  }

  InputBorder _getErrorBorder() {
    switch (widget.variant) {
      case InputVariant.outlined:
        return OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: AppColors.buttonText, width: 1),
        );
      case InputVariant.filled:
        return OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: AppColors.buttonText, width: 1),
        );
      case InputVariant.underlined:
        return const UnderlineInputBorder(
          borderSide: BorderSide(color: AppColors.buttonText, width: 1),
        );
    }
  }

  InputBorder _getFocusedErrorBorder() {
    switch (widget.variant) {
      case InputVariant.outlined:
        return OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: AppColors.buttonText, width: 2),
        );
      case InputVariant.filled:
        return OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: AppColors.buttonText, width: 2),
        );
      case InputVariant.underlined:
        return const UnderlineInputBorder(
          borderSide: BorderSide(color: AppColors.buttonText, width: 2),
        );
    }
  }

  InputBorder _getDisabledBorder() {
    switch (widget.variant) {
      case InputVariant.outlined:
        return OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(
            color: AppColors.textHint.withOpacity(0.2),
            width: 1,
          ),
        );
      case InputVariant.filled:
        return OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide.none,
        );
      case InputVariant.underlined:
        return UnderlineInputBorder(
          borderSide: BorderSide(
            color: AppColors.textHint.withOpacity(0.2),
            width: 1,
          ),
        );
    }
  }
}