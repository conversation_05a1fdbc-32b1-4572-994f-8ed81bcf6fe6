import 'package:flutter/foundation.dart';
import '../models/models.dart';
import '../services/services.dart';
import '../constants/app_constants.dart';

enum EmployeesState {
  initial,
  loading,
  loaded,
  error,
  creating,
  updating,
  deleting,
}

class EmployeesProvider extends ChangeNotifier {
  final ApiService _apiService = ApiService();
  final StorageService _storageService = StorageService();

  EmployeesState _state = EmployeesState.initial;
  List<User> _employees = [];
  User? _selectedEmployee;
  String? _errorMessage;
  bool _isLoading = false;
  int _currentPage = 1;
  bool _hasMoreData = true;
  String _searchQuery = '';
  String? _roleFilter;

  // Getters
  EmployeesState get state => _state;
  List<User> get employees => _employees;
  User? get selectedEmployee => _selectedEmployee;
  String? get errorMessage => _errorMessage;
  bool get isLoading => _isLoading;
  bool get hasMoreData => _hasMoreData;
  int get currentPage => _currentPage;
  String get searchQuery => _searchQuery;
  String? get roleFilter => _roleFilter;
  int get employeesCount => _employees.length;
  bool get hasEmployees => _employees.isNotEmpty;

  // Initialize
  Future<void> initialize() async {
    _setState(EmployeesState.loading);

    try {
      await _loadCachedEmployees();
      await refreshEmployees();
    } catch (e) {
      debugPrint('Employees Provider: Initialization error: $e');
      _setError('خطأ في تهيئة بيانات الموظفين');
    }
  }

  // Load cached employees
  Future<void> _loadCachedEmployees() async {
    try {
      final cachedEmployees = _storageService.getEmployees();
      if (cachedEmployees.isNotEmpty) {
        _employees = cachedEmployees;
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Employees Provider: Error loading cached employees: $e');
    }
  }

  // Refresh employees
  Future<void> refreshEmployees() async {
    _setLoading(true);
    _clearError();

    try {
      _currentPage = 1;
      _hasMoreData = true;
      _employees.clear();

      await _loadEmployees();
      _setState(EmployeesState.loaded);
    } catch (e) {
      debugPrint('Employees Provider: Error refreshing employees: $e');
      _setError('فشل في تحديث الموظفين');
    } finally {
      _setLoading(false);
    }
  }

  // Load employees
  Future<void> _loadEmployees() async {
    try {
      final response = await _apiService.getEmployees(
        page: _currentPage,
        perPage: AppConstants.defaultPageSize,
        search: _searchQuery.isNotEmpty ? _searchQuery : null,
        role: _roleFilter,
      );

      if (_currentPage == 1) {
        _employees = response.data;
      } else {
        _employees.addAll(response.data);
      }

      _hasMoreData = response.hasNextPage;

      // Cache employees
      await _storageService.saveEmployees(_employees);

      debugPrint('Employees Provider: Employees loaded (page $_currentPage)');
    } catch (e) {
      debugPrint('Employees Provider: Error loading employees: $e');
      rethrow;
    }
  }

  // Load more employees
  Future<void> loadMoreEmployees() async {
    if (!_hasMoreData || _isLoading) return;

    _setLoading(true);

    try {
      _currentPage++;
      await _loadEmployees();
    } catch (e) {
      debugPrint('Employees Provider: Error loading more employees: $e');
      _currentPage--; // Revert page increment
      _setError('فشل في تحميل المزيد من الموظفين');
    } finally {
      _setLoading(false);
    }
  }

  // Search employees
  Future<void> searchEmployees(String query) async {
    _searchQuery = query;
    _setLoading(true);
    _clearError();

    try {
      _currentPage = 1;
      _hasMoreData = true;
      _employees.clear();

      await _loadEmployees();
      debugPrint('Employees Provider: Employees searched with query: $query');
    } catch (e) {
      debugPrint('Employees Provider: Error searching employees: $e');
      _setError('فشل في البحث عن الموظفين');
    } finally {
      _setLoading(false);
    }
  }

  // Filter by role
  Future<void> filterByRole(String? role) async {
    _roleFilter = role;
    _setLoading(true);
    _clearError();

    try {
      _currentPage = 1;
      _hasMoreData = true;
      _employees.clear();

      await _loadEmployees();
      debugPrint('Employees Provider: Employees filtered by role: $role');
    } catch (e) {
      debugPrint('Employees Provider: Error filtering employees: $e');
      _setError('فشل في تصفية الموظفين');
    } finally {
      _setLoading(false);
    }
  }

  // Clear filters
  Future<void> clearFilters() async {
    if (_searchQuery.isEmpty && _roleFilter == null) return;

    _searchQuery = '';
    _roleFilter = null;
    await refreshEmployees();
  }

  // Get employee by ID
  Future<User?> getEmployee(int id) async {
    try {
      // First check if employee exists in local list
      final localEmployees = _employees.where((employee) => employee.id == id);

      if (localEmployees.isNotEmpty) {
        final localEmployee = localEmployees.first;
        _selectedEmployee = localEmployee;
        notifyListeners();
        return localEmployee;
      }
    } catch (e) {
      // Employee not found locally, fetch from API
    }

    try {
      final employee = await _apiService.getEmployee(id);
      _selectedEmployee = employee;
      notifyListeners();
      return employee;
    } catch (e) {
      debugPrint('Employees Provider: Error getting employee: $e');
      _setError('فشل في تحميل بيانات الموظف');
      return null;
    }
  }

  // Create employee
  Future<bool> createEmployee({
    required String name,
    required String email,
    required String password,
    required String role,
  }) async {
    _setState(EmployeesState.creating);
    _clearError();

    try {
      final request = UserCreateRequest(
        name: name,
        email: email,
        password: password,
        role: role,
      );

      final newEmployee = await _apiService.createEmployee(request);

      // Add to local list
      _employees.insert(0, newEmployee);

      // Update cache
      await _storageService.saveEmployees(_employees);

      _setState(EmployeesState.loaded);
      debugPrint('Employees Provider: Employee created successfully');
      return true;
    } catch (e) {
      debugPrint('Employees Provider: Error creating employee: $e');
      if (e is ApiException) {
        _setError(e.message);
      } else {
        _setError('فشل في إنشاء الموظف');
      }
      return false;
    }
  }

  // Update employee
  Future<bool> updateEmployee({
    required int id,
    String? name,
    String? email,
    String? password,
    String? role,
  }) async {
    _setState(EmployeesState.updating);
    _clearError();

    try {
      final request = UserUpdateRequest(
        name: name,
        email: email,
        password: password,
        role: role,
      );

      final updatedEmployee = await _apiService.updateEmployee(id, request);

      // Update in local list
      final index = _employees.indexWhere((employee) => employee.id == id);
      if (index != -1) {
        _employees[index] = updatedEmployee;
      }

      // Update selected employee if it's the same
      if (_selectedEmployee?.id == id) {
        _selectedEmployee = updatedEmployee;
      }

      // Update cache
      await _storageService.saveEmployees(_employees);

      _setState(EmployeesState.loaded);
      debugPrint('Employees Provider: Employee updated successfully');
      return true;
    } catch (e) {
      debugPrint('Employees Provider: Error updating employee: $e');
      if (e is ApiException) {
        _setError(e.message);
      } else {
        _setError('فشل في تحديث الموظف');
      }
      return false;
    }
  }

  // Delete employee
  Future<bool> deleteEmployee(int id) async {
    _setState(EmployeesState.deleting);
    _clearError();

    try {
      await _apiService.deleteEmployee(id);

      // Remove from local list
      _employees.removeWhere((employee) => employee.id == id);

      // Clear selected employee if it's the same
      if (_selectedEmployee?.id == id) {
        _selectedEmployee = null;
      }

      // Update cache
      await _storageService.saveEmployees(_employees);

      _setState(EmployeesState.loaded);
      debugPrint('Employees Provider: Employee deleted successfully');
      return true;
    } catch (e) {
      debugPrint('Employees Provider: Error deleting employee: $e');
      if (e is ApiException) {
        _setError(e.message);
      } else {
        _setError('فشل في حذف الموظف');
      }
      return false;
    }
  }

  // Get employees for dropdown/selection
  List<User> getEmployeesForSelection() {
    return _employees.toList();
  }

  // Get employees by role
  List<User> getEmployeesByRole(String role) {
    return _employees.where((employee) => employee.role == role).toList();
  }

  // Get admin employees
  List<User> getAdminEmployees() {
    return getEmployeesByRole(AppConstants.adminRole);
  }

  // Get regular employees
  List<User> getRegularEmployees() {
    return getEmployeesByRole(AppConstants.employeeRole);
  }

  // Find employee by email
  User? findEmployeeByEmail(String email) {
    try {
      return _employees.firstWhere(
        (employee) => employee.email.toLowerCase() == email.toLowerCase(),
      );
    } catch (e) {
      return null;
    }
  }

  // Get employee statistics
  Map<String, dynamic> getEmployeeStatistics() {
    final adminCount = getAdminEmployees().length;
    final employeeCount = getRegularEmployees().length;

    return {
      'total_employees': _employees.length,
      'admin_count': adminCount,
      'employee_count': employeeCount,
      'admin_percentage': _employees.isNotEmpty
          ? (adminCount / _employees.length * 100)
          : 0.0,
      'employee_percentage': _employees.isNotEmpty
          ? (employeeCount / _employees.length * 100)
          : 0.0,
    };
  }

  // Set selected employee
  void setSelectedEmployee(User? employee) {
    _selectedEmployee = employee;
    notifyListeners();
  }

  // Clear selected employee
  void clearSelectedEmployee() {
    _selectedEmployee = null;
    notifyListeners();
  }

  // Validate employee data
  Map<String, String?> validateEmployeeData({
    required String name,
    required String email,
    required String password,
    required String role,
    int? excludeId,
  }) {
    final errors = <String, String?>{};

    // Validate name
    if (name.trim().isEmpty) {
      errors['name'] = 'الاسم مطلوب';
    } else if (name.trim().length < 2) {
      errors['name'] = 'الاسم يجب أن يكون أكثر من حرفين';
    } else if (name.trim().length > AppConstants.maxNameLength) {
      errors['name'] = 'الاسم طويل جداً';
    }

    // Validate email
    if (email.trim().isEmpty) {
      errors['email'] = 'البريد الإلكتروني مطلوب';
    } else if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email)) {
      errors['email'] = 'البريد الإلكتروني غير صحيح';
    } else if (email.length > AppConstants.maxEmailLength) {
      errors['email'] = 'البريد الإلكتروني طويل جداً';
    } else {
      // Check if email already exists
      final existingEmployee = findEmployeeByEmail(email);
      if (existingEmployee != null && existingEmployee.id != excludeId) {
        errors['email'] = 'البريد الإلكتروني مستخدم بالفعل';
      }
    }

    // Validate password
    if (password.trim().isEmpty) {
      errors['password'] = 'كلمة المرور مطلوبة';
    } else if (password.length < AppConstants.minPasswordLength) {
      errors['password'] =
          'كلمة المرور يجب أن تكون ${AppConstants.minPasswordLength} أحرف على الأقل';
    }

    // Validate role
    if (role.trim().isEmpty) {
      errors['role'] = 'الدور مطلوب';
    } else if (role != AppConstants.adminRole &&
        role != AppConstants.employeeRole) {
      errors['role'] = 'الدور غير صحيح';
    }

    return errors;
  }

  // Private helper methods
  void _setState(EmployeesState newState) {
    _state = newState;
    notifyListeners();
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    _state = EmployeesState.error;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
    if (_state == EmployeesState.error) {
      _state = EmployeesState.loaded;
      notifyListeners();
    }
  }

  // Load employees data (alias for refreshEmployees)
  Future<void> loadEmployeesData() async {
    if (_state == EmployeesState.initial) {
      await initialize();
    } else {
      await refreshEmployees();
    }
  }
}
