import 'package:flutter/foundation.dart';
import '../models/models.dart';
import '../services/services.dart';

enum ReportsState { initial, loading, loaded, error }

class ReportsProvider with ChangeNotifier {
  final ReportsService _reportsService = ReportsService();

  // State management
  ReportsState _state = ReportsState.initial;
  String? _errorMessage;
  bool _isLoading = false;

  // Reports data
  List<ReportResponse> _generatedReports = [];
  AllEmployeesCheckResponse? _lastEmployeeCheck;
  List<PresenceVerificationResponse> _verificationHistory = [];

  // Filters
  DateTime _startDate = DateTime.now().subtract(const Duration(days: 30));
  DateTime _endDate = DateTime.now();
  int? _selectedUserId;
  int? _selectedSiteId;
  bool _includeStats = true;

  // Getters
  ReportsState get state => _state;
  String? get errorMessage => _errorMessage;
  bool get isLoading => _isLoading;
  List<ReportResponse> get generatedReports => _generatedReports;
  AllEmployeesCheckResponse? get lastEmployeeCheck => _lastEmployeeCheck;
  List<PresenceVerificationResponse> get verificationHistory =>
      _verificationHistory;
  DateTime get startDate => _startDate;
  DateTime get endDate => _endDate;
  int? get selectedUserId => _selectedUserId;
  int? get selectedSiteId => _selectedSiteId;
  bool get includeStats => _includeStats;

  // Computed getters
  bool get hasReports => _generatedReports.isNotEmpty;
  int get reportsCount => _generatedReports.length;
  bool get hasError => _state == ReportsState.error;
  bool get isInitial => _state == ReportsState.initial;

  // State management methods
  void _setState(ReportsState state) {
    _state = state;
    notifyListeners();
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String message) {
    _errorMessage = message;
    _setState(ReportsState.error);
  }

  void _clearError() {
    _errorMessage = null;
  }

  // Filter methods
  void setDateRange(DateTime start, DateTime end) {
    _startDate = start;
    _endDate = end;
    notifyListeners();
  }

  void setSelectedUser(int? userId) {
    _selectedUserId = userId;
    notifyListeners();
  }

  void setSelectedSite(int? siteId) {
    _selectedSiteId = siteId;
    notifyListeners();
  }

  void setIncludeStats(bool include) {
    _includeStats = include;
    notifyListeners();
  }

  // Report generation methods
  Future<bool> generateEmployeeReport() async {
    try {
      _setLoading(true);
      _clearError();

      final report = await _reportsService.generateEmployeeReport(
        startDate: _startDate.toIso8601String().split('T')[0],
        endDate: _endDate.toIso8601String().split('T')[0],
        includeStats: _includeStats,
      );

      _generatedReports.insert(0, report);
      _setState(ReportsState.loaded);

      debugPrint('ReportsProvider: Employee report generated successfully');
      return true;
    } catch (e) {
      debugPrint('ReportsProvider: Error generating employee report: $e');
      _setError('فشل في إنشاء تقرير الموظفين');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> generateIndividualReport(int userId) async {
    try {
      _setLoading(true);
      _clearError();

      final report = await _reportsService.generateIndividualReport(
        userId: userId,
        startDate: _startDate.toIso8601String().split('T')[0],
        endDate: _endDate.toIso8601String().split('T')[0],
      );

      _generatedReports.insert(0, report);
      _setState(ReportsState.loaded);

      debugPrint('ReportsProvider: Individual report generated successfully');
      return true;
    } catch (e) {
      debugPrint('ReportsProvider: Error generating individual report: $e');
      _setError('فشل في إنشاء التقرير الفردي');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> generateSiteReport(int siteId) async {
    try {
      _setLoading(true);
      _clearError();

      final report = await _reportsService.generateSiteReport(
        siteId: siteId,
        startDate: _startDate.toIso8601String().split('T')[0],
        endDate: _endDate.toIso8601String().split('T')[0],
      );

      _generatedReports.insert(0, report);
      _setState(ReportsState.loaded);

      debugPrint('ReportsProvider: Site report generated successfully');
      return true;
    } catch (e) {
      debugPrint('ReportsProvider: Error generating site report: $e');
      _setError('فشل في إنشاء تقرير الموقع');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Verification methods
  Future<bool> verifyEmployeePresence({
    required int userId,
    required double latitude,
    required double longitude,
    bool sendAlert = false,
  }) async {
    try {
      _setLoading(true);
      _clearError();

      final verification = await _reportsService.verifyEmployeePresence(
        userId: userId,
        latitude: latitude,
        longitude: longitude,
        sendAlert: sendAlert,
      );

      _verificationHistory.insert(0, verification);
      _setState(ReportsState.loaded);

      debugPrint('ReportsProvider: Employee presence verified successfully');
      return true;
    } catch (e) {
      debugPrint('ReportsProvider: Error verifying employee presence: $e');
      _setError('فشل في التحقق من حضور الموظف');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> checkAllActiveEmployees() async {
    try {
      _setLoading(true);
      _clearError();

      final result = await _reportsService.checkAllActiveEmployees();
      _lastEmployeeCheck = result;
      _setState(ReportsState.loaded);

      debugPrint('ReportsProvider: All employees checked successfully');
      return true;
    } catch (e) {
      debugPrint('ReportsProvider: Error checking all employees: $e');
      _setError('فشل في التحقق من جميع الموظفين');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Download methods
  Future<DownloadResult?> downloadReport(String filename) async {
    try {
      _setLoading(true);
      _clearError();

      final result = await _reportsService.downloadReport(filename);

      if (result.success) {
        if (result.isExcelFile) {
          debugPrint(
            'ReportsProvider: Excel file downloaded and saved to: ${result.filePath}',
          );
        } else {
          debugPrint(
            'ReportsProvider: CSV content downloaded, length: ${result.content.length}',
          );
          debugPrint(
            'ReportsProvider: First 200 characters: ${result.content.length > 200 ? result.content.substring(0, 200) : result.content}',
          );
        }
      } else {
        debugPrint('ReportsProvider: Download failed: ${result.errorMessage}');
        _setError(
          'فشل في تحميل التقرير: ${result.errorMessage ?? 'خطأ غير معروف'}',
        );
      }

      return result;
    } catch (e) {
      debugPrint('ReportsProvider: Error downloading report: $e');
      _setError('فشل في تحميل التقرير: ${e.toString()}');
      return null;
    } finally {
      _setLoading(false);
    }
  }

  // File management methods
  Future<bool> openExcelFile(String filePath) async {
    try {
      await _reportsService.openExcelFile(filePath);
      return true;
    } catch (e) {
      debugPrint('ReportsProvider: Error opening Excel file: $e');
      _setError('فشل في فتح الملف: ${e.toString()}');
      return false;
    }
  }

  Future<bool> shareExcelFile(String filePath, String filename) async {
    try {
      await _reportsService.shareExcelFile(filePath, filename);
      return true;
    } catch (e) {
      debugPrint('ReportsProvider: Error sharing Excel file: $e');
      _setError('فشل في مشاركة الملف: ${e.toString()}');
      return false;
    }
  }

  Future<bool> shareViaWhatsApp(String filePath, String filename) async {
    try {
      await _reportsService.shareViaWhatsApp(filePath, filename);
      return true;
    } catch (e) {
      debugPrint('ReportsProvider: Error sharing via WhatsApp: $e');
      _setError('فشل في مشاركة الملف عبر واتساب: ${e.toString()}');
      return false;
    }
  }

  Future<bool> shareViaEmail(String filePath, String filename) async {
    try {
      await _reportsService.shareViaEmail(filePath, filename);
      return true;
    } catch (e) {
      debugPrint('ReportsProvider: Error sharing via Email: $e');
      _setError('فشل في مشاركة الملف عبر الإيميل: ${e.toString()}');
      return false;
    }
  }

  Future<bool> deleteReport(String filePath) async {
    try {
      await _reportsService.deleteReport(filePath);
      return true;
    } catch (e) {
      debugPrint('ReportsProvider: Error deleting report: $e');
      _setError('فشل في حذف الملف: ${e.toString()}');
      return false;
    }
  }

  Future<List<FileInfo>> getSavedReports() async {
    try {
      final reports = await _reportsService.getSavedReports();
      return reports;
    } catch (e) {
      debugPrint('ReportsProvider: Error getting saved reports: $e');
      _setError('فشل في جلب الملفات المحفوظة: ${e.toString()}');
      return [];
    }
  }

  // Utility methods
  void clearReports() {
    _generatedReports.clear();
    notifyListeners();
  }

  void clearVerificationHistory() {
    _verificationHistory.clear();
    notifyListeners();
  }

  void removeReport(String filename) {
    _generatedReports.removeWhere((report) => report.filename == filename);
    notifyListeners();
  }

  // Initialize provider
  Future<void> initialize() async {
    _setState(ReportsState.loading);

    try {
      // Load any cached data if needed
      _setState(ReportsState.loaded);
    } catch (e) {
      debugPrint('ReportsProvider: Initialization error: $e');
      _setError('خطأ في تهيئة بيانات التقارير');
    }
  }

  // Reset provider
  void reset() {
    _state = ReportsState.initial;
    _errorMessage = null;
    _isLoading = false;
    _generatedReports.clear();
    _lastEmployeeCheck = null;
    _verificationHistory.clear();
    _startDate = DateTime.now().subtract(const Duration(days: 30));
    _endDate = DateTime.now();
    _selectedUserId = null;
    _selectedSiteId = null;
    _includeStats = true;
    notifyListeners();
  }
}