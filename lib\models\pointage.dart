import 'package:json_annotation/json_annotation.dart';
import 'package:equatable/equatable.dart';
import 'user.dart';
import 'site.dart';

part 'pointage.g.dart';

@JsonSerializable()
class Pointage extends Equatable {
  final int id;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'user_id')
  final int userId;
  @<PERSON>son<PERSON><PERSON>(name: 'site_id')
  final int siteId;
  @<PERSON>son<PERSON>ey(name: 'debut_pointage')
  final DateTime debutPointage;
  @<PERSON>son<PERSON><PERSON>(name: 'fin_pointage')
  final DateTime? finPointage;
  final String? duree;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'debut_latitude')
  final String? debutLatitude;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'debut_longitude')
  final String? debutLongitude;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'fin_latitude')
  final String? finLatitude;
  @Json<PERSON>ey(name: 'fin_longitude')
  final String? finLongitude;
  @<PERSON>son<PERSON><PERSON>(name: 'created_at')
  final String? createdAt;
  @JsonKey(name: 'updated_at')
  final String? updatedAt;
  final User? user;
  final Site? site;

  const Pointage({
    required this.id,
    required this.userId,
    required this.siteId,
    required this.debutPointage,
    this.finPointage,
    this.duree,
    this.debutLatitude,
    this.debutLongitude,
    this.finLatitude,
    this.finLongitude,
    this.createdAt,
    this.updatedAt,
    this.user,
    this.site,
  });

  factory Pointage.fromJson(Map<String, dynamic> json) => _$PointageFromJson(json);

  Map<String, dynamic> toJson() => _$PointageToJson(this);

  Pointage copyWith({
    int? id,
    int? userId,
    int? siteId,
    DateTime? debutPointage,
    DateTime? finPointage,
    String? duree,
    String? debutLatitude,
    String? debutLongitude,
    String? finLatitude,
    String? finLongitude,
    String? createdAt,
    String? updatedAt,
    User? user,
    Site? site,
  }) {
    return Pointage(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      siteId: siteId ?? this.siteId,
      debutPointage: debutPointage ?? this.debutPointage,
      finPointage: finPointage ?? this.finPointage,
      duree: duree ?? this.duree,
      debutLatitude: debutLatitude ?? this.debutLatitude,
      debutLongitude: debutLongitude ?? this.debutLongitude,
      finLatitude: finLatitude ?? this.finLatitude,
      finLongitude: finLongitude ?? this.finLongitude,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      user: user ?? this.user,
      site: site ?? this.site,
    );
  }

  bool get isActive => finPointage == null;
  bool get isCompleted => finPointage != null;
  bool get hasStartLocation => debutLatitude != null && debutLongitude != null;
  bool get hasEndLocation => finLatitude != null && finLongitude != null;

  String get status => isActive ? 'نشط' : isCompleted ? 'مكتمل' : 'غير محدد';
  String get statusColor => isActive ? 'success' : isCompleted ? 'info' : 'warning';

  String get displayStartTime {
    return '${debutPointage.hour.toString().padLeft(2, '0')}:${debutPointage.minute.toString().padLeft(2, '0')}';
  }

  String get displayEndTime {
    if (finPointage == null) return 'لم ينته';
    return '${finPointage!.hour.toString().padLeft(2, '0')}:${finPointage!.minute.toString().padLeft(2, '0')}';
  }

  String get displayDate {
    return '${debutPointage.day.toString().padLeft(2, '0')}/${debutPointage.month.toString().padLeft(2, '0')}/${debutPointage.year}';
  }

  String get displayDuration {
    if (duree == null) return isActive ? 'جاري...' : 'غير محدد';
    return duree!;
  }

  String get userName => user?.name ?? 'غير محدد';
  String get siteName => site?.name ?? 'غير محدد';

  double? get startLatitude {
    if (debutLatitude == null) return null;
    return double.tryParse(debutLatitude!);
  }

  double? get startLongitude {
    if (debutLongitude == null) return null;
    return double.tryParse(debutLongitude!);
  }

  double? get endLatitude {
    if (finLatitude == null) return null;
    return double.tryParse(finLatitude!);
  }

  double? get endLongitude {
    if (finLongitude == null) return null;
    return double.tryParse(finLongitude!);
  }

  @override
  List<Object?> get props => [
        id,
        userId,
        siteId,
        debutPointage,
        finPointage,
        duree,
        debutLatitude,
        debutLongitude,
        finLatitude,
        finLongitude,
        createdAt,
        updatedAt,
        user,
        site,
      ];

  @override
  String toString() {
    return 'Pointage{id: $id, userId: $userId, siteId: $siteId, status: $status}';
  }
}

@JsonSerializable()
class PointageCreateRequest extends Equatable {
  final double latitude;
  final double longitude;
  final double? accuracy;
  final int timestamp; // Obligatoire pour forcer l'utilisation de l'heure exacte
  final String? exactTime; // Heure exacte en format ISO pour double vérification

  const PointageCreateRequest({
    required this.latitude,
    required this.longitude,
    required this.timestamp, // Maintenant obligatoire
    this.accuracy,
    this.exactTime,
  });

  factory PointageCreateRequest.fromJson(Map<String, dynamic> json) =>
      _$PointageCreateRequestFromJson(json);

  Map<String, dynamic> toJson() => _$PointageCreateRequestToJson(this);

  @override
  List<Object?> get props => [latitude, longitude, accuracy, timestamp];
}

@JsonSerializable()
class PointageResponse extends Equatable {
  final Pointage pointage;
  final String type; // 'check_in' or 'check_out'
  final String message;
  @JsonKey(name: 'message_ar')
  final String messageAr;

  const PointageResponse({
    required this.pointage,
    required this.type,
    required this.message,
    required this.messageAr,
  });

  factory PointageResponse.fromJson(Map<String, dynamic> json) =>
      _$PointageResponseFromJson(json);

  Map<String, dynamic> toJson() => _$PointageResponseToJson(this);

  bool get isCheckIn => type == 'check_in';
  bool get isCheckOut => type == 'check_out';

  @override
  List<Object?> get props => [pointage, type, message, messageAr];
}

@JsonSerializable()
class PointageStats extends Equatable {
  @JsonKey(name: 'total_pointages')
  final int totalPointages;
  @JsonKey(name: 'active_pointages')
  final int activePointages;
  @JsonKey(name: 'completed_pointages')
  final int completedPointages;
  @JsonKey(name: 'total_hours')
  final double totalHours;
  @JsonKey(name: 'average_hours')
  final double averageHours;
  @JsonKey(name: 'this_month_hours')
  final double thisMonthHours;
  @JsonKey(name: 'this_week_hours')
  final double thisWeekHours;

  const PointageStats({
    required this.totalPointages,
    required this.activePointages,
    required this.completedPointages,
    required this.totalHours,
    required this.averageHours,
    required this.thisMonthHours,
    required this.thisWeekHours,
  });

  factory PointageStats.fromJson(Map<String, dynamic> json) =>
      _$PointageStatsFromJson(json);

  Map<String, dynamic> toJson() => _$PointageStatsToJson(this);

  String get totalHoursText => '${totalHours.toStringAsFixed(1)} ساعة';
  String get averageHoursText => '${averageHours.toStringAsFixed(1)} ساعة';
  String get thisMonthHoursText => '${thisMonthHours.toStringAsFixed(1)} ساعة';
  String get thisWeekHoursText => '${thisWeekHours.toStringAsFixed(1)} ساعة';

  @override
  List<Object?> get props => [
        totalPointages,
        activePointages,
        completedPointages,
        totalHours,
        averageHours,
        thisMonthHours,
        thisWeekHours,
      ];
}

@JsonSerializable()
class CheckInRequest {
  @JsonKey(name: 'site_id')
  final int siteId;
  final double latitude;
  final double longitude;

  const CheckInRequest({
    required this.siteId,
    required this.latitude,
    required this.longitude,
  });

  factory CheckInRequest.fromJson(Map<String, dynamic> json) => _$CheckInRequestFromJson(json);

  Map<String, dynamic> toJson() => _$CheckInRequestToJson(this);
}

@JsonSerializable()
class CheckOutRequest {
  final double latitude;
  final double longitude;

  const CheckOutRequest({
    required this.latitude,
    required this.longitude,
  });

  factory CheckOutRequest.fromJson(Map<String, dynamic> json) => _$CheckOutRequestFromJson(json);

  Map<String, dynamic> toJson() => _$CheckOutRequestToJson(this);
}
