import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:geolocator/geolocator.dart';
import '../models/site.dart';
import '../services/api_service.dart';
import '../services/location_service.dart';
import '../services/notification_service.dart';


/// Service de surveillance des employés sur les chantiers
class MonitoringService extends ChangeNotifier {
  final ApiService _apiService = ApiService();
  final LocationService _locationService = LocationService();
  final NotificationService _notificationService = NotificationService();

  // État de la surveillance
  bool _isMonitoring = false;
  Timer? _monitoringTimer;
  StreamSubscription<Position>? _positionSubscription;

  // Données de surveillance
  final List<EmployeeMonitoringData> _monitoredEmployees = [];
  final List<SiteMonitoringData> _monitoredSites = [];
  final Map<int, Position> _lastKnownPositions = {};
  final Map<int, DateTime> _lastCheckTimes = {};

  // Configuration
  Duration _checkInterval = const Duration(minutes: 5);
  final int _maxMissedChecks = 3;

  // Getters
  bool get isMonitoring => _isMonitoring;
  List<EmployeeMonitoringData> get monitoredEmployees => _monitoredEmployees;
  List<SiteMonitoringData> get monitoredSites => _monitoredSites;
  Duration get checkInterval => _checkInterval;

  /// Démarre la surveillance pour un employé spécifique
  Future<void> startEmployeeMonitoring(int userId, {Duration? interval}) async {
    try {
      if (interval != null) {
        _checkInterval = interval;
      }

      await _apiService.startMonitoring(userId: userId, intervalMinutes: _checkInterval.inMinutes);
      
      // Ajouter l'employé à la liste de surveillance
      final existingIndex = _monitoredEmployees.indexWhere((e) => e.userId == userId);
      if (existingIndex == -1) {
        _monitoredEmployees.add(EmployeeMonitoringData(
          userId: userId,
          isActive: true,
          lastCheckTime: DateTime.now(),
          missedChecks: 0,
        ));
      } else {
        _monitoredEmployees[existingIndex] = _monitoredEmployees[existingIndex].copyWith(
          isActive: true,
          lastCheckTime: DateTime.now(),
          missedChecks: 0,
        );
      }

      if (!_isMonitoring) {
        await _startGlobalMonitoring();
      }

      notifyListeners();
    } catch (e) {
      debugPrint('Erreur lors du démarrage de la surveillance: $e');
      rethrow;
    }
  }

  /// Arrête la surveillance pour un employé spécifique
  Future<void> stopEmployeeMonitoring(int userId) async {
    try {
      await _apiService.stopMonitoring(userId: userId);
      
      _monitoredEmployees.removeWhere((e) => e.userId == userId);
      _lastKnownPositions.remove(userId);
      _lastCheckTimes.remove(userId);

      if (_monitoredEmployees.isEmpty) {
        await _stopGlobalMonitoring();
      }

      notifyListeners();
    } catch (e) {
      debugPrint('Erreur lors de l\'arrêt de la surveillance: $e');
      rethrow;
    }
  }

  /// Démarre la surveillance globale
  Future<void> _startGlobalMonitoring() async {
    if (_isMonitoring) return;

    _isMonitoring = true;
    
    // Démarrer le timer de vérification périodique
    _monitoringTimer = Timer.periodic(_checkInterval, (_) => _performPeriodicCheck());
    
    // Démarrer l'écoute de la position si c'est l'employé actuel
    await _startPositionTracking();
    
    notifyListeners();
  }

  /// Arrête la surveillance globale
  Future<void> _stopGlobalMonitoring() async {
    _isMonitoring = false;
    
    _monitoringTimer?.cancel();
    _monitoringTimer = null;
    
    await _positionSubscription?.cancel();
    _positionSubscription = null;
    
    notifyListeners();
  }

  /// Démarre le suivi de position
  Future<void> _startPositionTracking() async {
    try {
      _positionSubscription = _locationService.getPositionStream().listen(
        (position) => _onPositionUpdate(position),
        onError: (error) => debugPrint('Erreur de position: $error'),
      );
    } catch (e) {
      debugPrint('Erreur lors du démarrage du suivi de position: $e');
    }
  }

  /// Callback lors de la mise à jour de position
  void _onPositionUpdate(Position position) {
    // Mettre à jour la position de l'utilisateur actuel
    // Note: Vous devrez obtenir l'ID de l'utilisateur actuel depuis AuthProvider
    // _lastKnownPositions[currentUserId] = position;
    
    // Vérifier la présence sur site
    _checkPresenceOnSite(position);
  }

  /// Vérifie la présence sur site
  void _checkPresenceOnSite(Position position) {
    for (final site in _monitoredSites) {
      final distance = Geolocator.distanceBetween(
        position.latitude,
        position.longitude,
        site.latitude,
        site.longitude,
      );

      final isOnSite = distance <= site.radius;
      
      if (isOnSite != site.hasEmployeesPresent) {
        site.hasEmployeesPresent = isOnSite;
        
        // Notifier le changement de statut
        _notificationService.showNotification(
          title: isOnSite ? 'دخول الموقع' : 'مغادرة الموقع',
          body: isOnSite 
            ? 'دخلت إلى موقع ${site.name}'
            : 'غادرت موقع ${site.name}',
        );
      }
    }
  }

  /// Effectue une vérification périodique
  Future<void> _performPeriodicCheck() async {
    try {
      // Vérifier tous les employés actifs
      await _apiService.checkAllActiveEmployees();
      
      // Mettre à jour les données de surveillance
      await _updateMonitoringData();
      
      // Vérifier les employés manqués
      _checkMissedEmployees();
      
    } catch (e) {
      debugPrint('Erreur lors de la vérification périodique: $e');
    }
  }

  /// Met à jour les données de surveillance
  Future<void> _updateMonitoringData() async {
    for (int i = 0; i < _monitoredEmployees.length; i++) {
      final employee = _monitoredEmployees[i];
      final lastCheck = _lastCheckTimes[employee.userId];
      
      if (lastCheck != null) {
        final timeSinceLastCheck = DateTime.now().difference(lastCheck);
        
        if (timeSinceLastCheck > _checkInterval * 2) {
          // Employé manqué
          _monitoredEmployees[i] = employee.copyWith(
            missedChecks: employee.missedChecks + 1,
            isActive: employee.missedChecks < _maxMissedChecks,
          );
        }
      }
    }
    
    notifyListeners();
  }

  /// Vérifie les employés manqués
  void _checkMissedEmployees() {
    for (final employee in _monitoredEmployees) {
      if (employee.missedChecks >= _maxMissedChecks && employee.isActive) {
        // Notifier l'administrateur
        _notificationService.showNotification(
          title: 'تحذير: موظف غير متاح',
          body: 'الموظف ${employee.userId} لم يتم العثور عليه لفترة طويلة',
        );
      }
    }
  }

  /// Vérifie un employé spécifique sur un site
  Future<void> checkEmployeeOnSite(int userId, double latitude, double longitude) async {
    try {
      await _apiService.checkEmployeeOnSite(userId: userId, latitude: latitude, longitude: longitude);
      
      // Mettre à jour la dernière vérification
      _lastCheckTimes[userId] = DateTime.now();
      _lastKnownPositions[userId] = Position(
        latitude: latitude,
        longitude: longitude,
        timestamp: DateTime.now(),
        accuracy: 0,
        altitude: 0,
        altitudeAccuracy: 0,
        heading: 0,
        headingAccuracy: 0,
        speed: 0,
        speedAccuracy: 0,
      );
      
      notifyListeners();
    } catch (e) {
      debugPrint('Erreur lors de la vérification de l\'employé: $e');
      rethrow;
    }
  }

  /// Ajoute un site à surveiller
  void addSiteToMonitoring(Site site) {
    final existingIndex = _monitoredSites.indexWhere((s) => s.id == site.id);
    if (existingIndex == -1) {
      _monitoredSites.add(SiteMonitoringData.fromSite(site));
      notifyListeners();
    }
  }

  /// Supprime un site de la surveillance
  void removeSiteFromMonitoring(int siteId) {
    _monitoredSites.removeWhere((s) => s.id == siteId);
    notifyListeners();
  }

  /// Configure l'intervalle de vérification
  void setCheckInterval(Duration interval) {
    _checkInterval = interval;
    
    if (_isMonitoring) {
      // Redémarrer le timer avec le nouvel intervalle
      _monitoringTimer?.cancel();
      _monitoringTimer = Timer.periodic(_checkInterval, (_) => _performPeriodicCheck());
    }
  }

  /// Nettoie les ressources
  @override
  void dispose() {
    _stopGlobalMonitoring();
    super.dispose();
  }
}

/// Données de surveillance d'un employé
class EmployeeMonitoringData {
  final int userId;
  final bool isActive;
  final DateTime lastCheckTime;
  final int missedChecks;
  final Position? lastKnownPosition;

  const EmployeeMonitoringData({
    required this.userId,
    required this.isActive,
    required this.lastCheckTime,
    required this.missedChecks,
    this.lastKnownPosition,
  });

  EmployeeMonitoringData copyWith({
    int? userId,
    bool? isActive,
    DateTime? lastCheckTime,
    int? missedChecks,
    Position? lastKnownPosition,
  }) {
    return EmployeeMonitoringData(
      userId: userId ?? this.userId,
      isActive: isActive ?? this.isActive,
      lastCheckTime: lastCheckTime ?? this.lastCheckTime,
      missedChecks: missedChecks ?? this.missedChecks,
      lastKnownPosition: lastKnownPosition ?? this.lastKnownPosition,
    );
  }
}

/// Données de surveillance d'un site
class SiteMonitoringData {
  final int id;
  final String name;
  final double latitude;
  final double longitude;
  final double radius;
  bool hasEmployeesPresent;
  final List<int> assignedEmployeeIds;

  SiteMonitoringData({
    required this.id,
    required this.name,
    required this.latitude,
    required this.longitude,
    required this.radius,
    this.hasEmployeesPresent = false,
    this.assignedEmployeeIds = const [],
  });

  factory SiteMonitoringData.fromSite(Site site) {
    return SiteMonitoringData(
      id: site.id,
      name: site.nom,
      latitude: site.latitude,
      longitude: site.longitude,
      radius: site.rayon,
      assignedEmployeeIds: site.assignedEmployeeIds ?? [],
    );
  }
}
