import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart' as intl;

import 'app_localizations_ar.dart';

// ignore_for_file: type=lint

/// Callers can lookup localized strings with an instance of AppLocalizations
/// returned by `AppLocalizations.of(context)`.
///
/// Applications need to include `AppLocalizations.delegate()` in their app's
/// `localizationDelegates` list, and the locales they support in the app's
/// `supportedLocales` list. For example:
///
/// ```dart
/// import 'l10n/app_localizations.dart';
///
/// return MaterialApp(
///   localizationsDelegates: AppLocalizations.localizationsDelegates,
///   supportedLocales: AppLocalizations.supportedLocales,
///   home: MyApplicationHome(),
/// );
/// ```
///
/// ## Update pubspec.yaml
///
/// Please make sure to update your pubspec.yaml to include the following
/// packages:
///
/// ```yaml
/// dependencies:
///   # Internationalization support.
///   flutter_localizations:
///     sdk: flutter
///   intl: any # Use the pinned version from flutter_localizations
///
///   # Rest of dependencies
/// ```
///
/// ## iOS Applications
///
/// iOS applications define key application metadata, including supported
/// locales, in an Info.plist file that is built into the application bundle.
/// To configure the locales supported by your app, you’ll need to edit this
/// file.
///
/// First, open your project’s ios/Runner.xcworkspace Xcode workspace file.
/// Then, in the Project Navigator, open the Info.plist file under the Runner
/// project’s Runner folder.
///
/// Next, select the Information Property List item, select Add Item from the
/// Editor menu, then select Localizations from the pop-up menu.
///
/// Select and expand the newly-created Localizations item then, for each
/// locale your application supports, add a new item and select the locale
/// you wish to add from the pop-up menu in the Value field. This list should
/// be consistent with the languages listed in the AppLocalizations.supportedLocales
/// property.
abstract class AppLocalizations {
  AppLocalizations(String locale)
    : localeName = intl.Intl.canonicalizedLocale(locale.toString());

  final String localeName;

  static AppLocalizations of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations)!;
  }

  static const LocalizationsDelegate<AppLocalizations> delegate =
      _AppLocalizationsDelegate();

  /// A list of this localizations delegate along with the default localizations
  /// delegates.
  ///
  /// Returns a list of localizations delegates containing this delegate along with
  /// GlobalMaterialLocalizations.delegate, GlobalCupertinoLocalizations.delegate,
  /// and GlobalWidgetsLocalizations.delegate.
  ///
  /// Additional delegates can be added by appending to this list in
  /// MaterialApp. This list does not have to be used at all if a custom list
  /// of delegates is preferred or required.
  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates =
      <LocalizationsDelegate<dynamic>>[
        delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
      ];

  /// A list of this localizations delegate's supported locales.
  static const List<Locale> supportedLocales = <Locale>[Locale('ar')];

  /// The title of the application
  ///
  /// In ar, this message translates to:
  /// **'نظام الحضور والانصراف'**
  String get appTitle;

  /// Login button text
  ///
  /// In ar, this message translates to:
  /// **'تسجيل الدخول'**
  String get login;

  /// Logout button text
  ///
  /// In ar, this message translates to:
  /// **'تسجيل الخروج'**
  String get logout;

  /// Email field label
  ///
  /// In ar, this message translates to:
  /// **'البريد الإلكتروني'**
  String get email;

  /// Password field label
  ///
  /// In ar, this message translates to:
  /// **'كلمة المرور'**
  String get password;

  /// Dashboard screen title
  ///
  /// In ar, this message translates to:
  /// **'لوحة التحكم'**
  String get dashboard;

  /// Check-in button text
  ///
  /// In ar, this message translates to:
  /// **'تسجيل الحضور'**
  String get checkIn;

  /// Check-out button text
  ///
  /// In ar, this message translates to:
  /// **'تسجيل الانصراف'**
  String get checkOut;

  /// Current status label
  ///
  /// In ar, this message translates to:
  /// **'الحالة الحالية'**
  String get currentStatus;

  /// Assigned site label
  ///
  /// In ar, this message translates to:
  /// **'الموقع المخصص'**
  String get assignedSite;

  /// Attendance history screen title
  ///
  /// In ar, this message translates to:
  /// **'سجل الحضور'**
  String get attendanceHistory;

  /// Employees screen title
  ///
  /// In ar, this message translates to:
  /// **'الموظفون'**
  String get employees;

  /// Sites screen title
  ///
  /// In ar, this message translates to:
  /// **'المواقع'**
  String get sites;

  /// Reports screen title
  ///
  /// In ar, this message translates to:
  /// **'التقارير'**
  String get reports;

  /// Settings screen title
  ///
  /// In ar, this message translates to:
  /// **'الإعدادات'**
  String get settings;

  /// Loading message
  ///
  /// In ar, this message translates to:
  /// **'جاري التحميل...'**
  String get loading;

  /// Error message
  ///
  /// In ar, this message translates to:
  /// **'خطأ'**
  String get error;

  /// Success message
  ///
  /// In ar, this message translates to:
  /// **'نجح'**
  String get success;

  /// Cancel button text
  ///
  /// In ar, this message translates to:
  /// **'إلغاء'**
  String get cancel;

  /// Confirm button text
  ///
  /// In ar, this message translates to:
  /// **'تأكيد'**
  String get confirm;

  /// Save button text
  ///
  /// In ar, this message translates to:
  /// **'حفظ'**
  String get save;

  /// Delete button text
  ///
  /// In ar, this message translates to:
  /// **'حذف'**
  String get delete;

  /// Edit button text
  ///
  /// In ar, this message translates to:
  /// **'تعديل'**
  String get edit;

  /// Add button text
  ///
  /// In ar, this message translates to:
  /// **'إضافة'**
  String get add;

  /// Search placeholder text
  ///
  /// In ar, this message translates to:
  /// **'بحث'**
  String get search;

  /// No data found message
  ///
  /// In ar, this message translates to:
  /// **'لا توجد بيانات'**
  String get noDataFound;

  /// Location permission required message
  ///
  /// In ar, this message translates to:
  /// **'مطلوب إذن الموقع'**
  String get locationPermissionRequired;

  /// GPS not enabled message
  ///
  /// In ar, this message translates to:
  /// **'GPS غير مفعل'**
  String get gpsNotEnabled;

  /// Out of range message
  ///
  /// In ar, this message translates to:
  /// **'خارج النطاق المسموح'**
  String get outOfRange;

  /// Check-in success message
  ///
  /// In ar, this message translates to:
  /// **'تم تسجيل الحضور بنجاح'**
  String get checkInSuccess;

  /// Check-out success message
  ///
  /// In ar, this message translates to:
  /// **'تم تسجيل الانصراف بنجاح'**
  String get checkOutSuccess;

  /// Invalid credentials error message
  ///
  /// In ar, this message translates to:
  /// **'بيانات الدخول غير صحيحة'**
  String get invalidCredentials;

  /// Network error message
  ///
  /// In ar, this message translates to:
  /// **'خطأ في الشبكة'**
  String get networkError;

  /// Server error message
  ///
  /// In ar, this message translates to:
  /// **'خطأ في الخادم'**
  String get serverError;

  /// Name field label
  ///
  /// In ar, this message translates to:
  /// **'الاسم'**
  String get name;

  /// Role field label
  ///
  /// In ar, this message translates to:
  /// **'الدور'**
  String get role;

  /// Admin role
  ///
  /// In ar, this message translates to:
  /// **'مدير'**
  String get admin;

  /// Employee role
  ///
  /// In ar, this message translates to:
  /// **'موظف'**
  String get employee;

  /// Latitude label
  ///
  /// In ar, this message translates to:
  /// **'خط العرض'**
  String get latitude;

  /// Longitude label
  ///
  /// In ar, this message translates to:
  /// **'خط الطول'**
  String get longitude;

  /// Start time label
  ///
  /// In ar, this message translates to:
  /// **'وقت البداية'**
  String get startTime;

  /// End time label
  ///
  /// In ar, this message translates to:
  /// **'وقت النهاية'**
  String get endTime;

  /// Duration label
  ///
  /// In ar, this message translates to:
  /// **'المدة'**
  String get duration;

  /// Date label
  ///
  /// In ar, this message translates to:
  /// **'التاريخ'**
  String get date;

  /// Time label
  ///
  /// In ar, this message translates to:
  /// **'الوقت'**
  String get time;

  /// Today label
  ///
  /// In ar, this message translates to:
  /// **'اليوم'**
  String get today;

  /// Yesterday label
  ///
  /// In ar, this message translates to:
  /// **'أمس'**
  String get yesterday;

  /// This week label
  ///
  /// In ar, this message translates to:
  /// **'هذا الأسبوع'**
  String get thisWeek;

  /// This month label
  ///
  /// In ar, this message translates to:
  /// **'هذا الشهر'**
  String get thisMonth;

  /// Active status
  ///
  /// In ar, this message translates to:
  /// **'نشط'**
  String get active;

  /// Inactive status
  ///
  /// In ar, this message translates to:
  /// **'غير نشط'**
  String get inactive;

  /// Present status
  ///
  /// In ar, this message translates to:
  /// **'حاضر'**
  String get present;

  /// Absent status
  ///
  /// In ar, this message translates to:
  /// **'غائب'**
  String get absent;

  /// Working hours label
  ///
  /// In ar, this message translates to:
  /// **'ساعات العمل'**
  String get workingHours;

  /// Total hours label
  ///
  /// In ar, this message translates to:
  /// **'إجمالي الساعات'**
  String get totalHours;

  /// Overtime label
  ///
  /// In ar, this message translates to:
  /// **'ساعات إضافية'**
  String get overtime;

  /// Refresh button text
  ///
  /// In ar, this message translates to:
  /// **'تحديث'**
  String get refresh;

  /// Retry button text
  ///
  /// In ar, this message translates to:
  /// **'إعادة المحاولة'**
  String get retry;

  /// Close button text
  ///
  /// In ar, this message translates to:
  /// **'إغلاق'**
  String get close;

  /// Back button text
  ///
  /// In ar, this message translates to:
  /// **'رجوع'**
  String get back;

  /// Next button text
  ///
  /// In ar, this message translates to:
  /// **'التالي'**
  String get next;

  /// Previous button text
  ///
  /// In ar, this message translates to:
  /// **'السابق'**
  String get previous;

  /// Filter button text
  ///
  /// In ar, this message translates to:
  /// **'تصفية'**
  String get filter;

  /// Sort button text
  ///
  /// In ar, this message translates to:
  /// **'ترتيب'**
  String get sort;

  /// Export button text
  ///
  /// In ar, this message translates to:
  /// **'تصدير'**
  String get export;

  /// Import button text
  ///
  /// In ar, this message translates to:
  /// **'استيراد'**
  String get import;

  /// Profile screen title
  ///
  /// In ar, this message translates to:
  /// **'الملف الشخصي'**
  String get profile;

  /// Change password button text
  ///
  /// In ar, this message translates to:
  /// **'تغيير كلمة المرور'**
  String get changePassword;

  /// Current password field label
  ///
  /// In ar, this message translates to:
  /// **'كلمة المرور الحالية'**
  String get currentPassword;

  /// New password field label
  ///
  /// In ar, this message translates to:
  /// **'كلمة المرور الجديدة'**
  String get newPassword;

  /// Confirm password field label
  ///
  /// In ar, this message translates to:
  /// **'تأكيد كلمة المرور'**
  String get confirmPassword;

  /// Password changed success message
  ///
  /// In ar, this message translates to:
  /// **'تم تغيير كلمة المرور بنجاح'**
  String get passwordChanged;

  /// Field required validation message
  ///
  /// In ar, this message translates to:
  /// **'هذا الحقل مطلوب'**
  String get fieldRequired;

  /// Invalid email validation message
  ///
  /// In ar, this message translates to:
  /// **'البريد الإلكتروني غير صحيح'**
  String get invalidEmail;

  /// Password too short validation message
  ///
  /// In ar, this message translates to:
  /// **'كلمة المرور قصيرة جداً'**
  String get passwordTooShort;

  /// Passwords do not match validation message
  ///
  /// In ar, this message translates to:
  /// **'كلمات المرور غير متطابقة'**
  String get passwordsDoNotMatch;
}

class _AppLocalizationsDelegate
    extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  Future<AppLocalizations> load(Locale locale) {
    return SynchronousFuture<AppLocalizations>(lookupAppLocalizations(locale));
  }

  @override
  bool isSupported(Locale locale) =>
      <String>['ar'].contains(locale.languageCode);

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}

AppLocalizations lookupAppLocalizations(Locale locale) {
  // Lookup logic when only language code is specified.
  switch (locale.languageCode) {
    case 'ar':
      return AppLocalizationsAr();
  }

  throw FlutterError(
    'AppLocalizations.delegate failed to load unsupported locale "$locale". This is likely '
    'an issue with the localizations generation tool. Please file an issue '
    'on GitHub with a reproducible sample app and the gen-l10n configuration '
    'that was used.',
  );
}
