import 'package:flutter/material.dart';
import 'dart:math' as math;

class FullCoverageLogo extends StatelessWidget {
  final double size;
  final bool showBackground;

  const FullCoverageLogo({
    super.key,
    this.size = 120,
    this.showBackground = true,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: size,
      height: size,
      decoration: showBackground
          ? BoxDecoration(
              borderRadius: BorderRadius.circular(size * 0.1),
              gradient: RadialGradient(
                center: Alignment.center,
                radius: 0.7,
                colors: [const Color(0xFF34495E), const Color(0xFF2C3E50)],
              ),
            )
          : null,
      child: CustomPaint(
        size: Size(size, size),
        painter: FullCoverageLogoPainter(),
      ),
    );
  }
}

class FullCoverageLogoPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 2;

    // Define colors with gradients
    final blueGradient = LinearGradient(
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
      colors: [const Color(0xFF5DADE2), const Color(0xFF3498DB)],
    );

    final redGradient = LinearGradient(
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
      colors: [const Color(0xFFEC7063), const Color(0xFFE74C3C)],
    );

    // Blue square background (top-left)
    final blueRect = Rect.fromLTWH(0, 0, size.width * 0.4, size.height * 0.4);
    final blueRectPaint = Paint()..shader = blueGradient.createShader(blueRect);
    canvas.drawRRect(
      RRect.fromRectAndRadius(blueRect, Radius.circular(size.width * 0.05)),
      blueRectPaint,
    );

    // Red square background (bottom-right)
    final redRect = Rect.fromLTWH(
      size.width * 0.6,
      size.height * 0.6,
      size.width * 0.4,
      size.height * 0.4,
    );
    final redRectPaint = Paint()..shader = redGradient.createShader(redRect);
    canvas.drawRRect(
      RRect.fromRectAndRadius(redRect, Radius.circular(size.width * 0.05)),
      redRectPaint,
    );

    // Main clock circle
    final clockPaint = Paint()
      ..color = const Color(0xFF2C3E50)
      ..style = PaintingStyle.fill;
    canvas.drawCircle(center, radius * 0.65, clockPaint);

    // Clock border
    final borderPaint = Paint()
      ..color = const Color(0xFF34495E)
      ..style = PaintingStyle.stroke
      ..strokeWidth = size.width * 0.02;
    canvas.drawCircle(center, radius * 0.65, borderPaint);

    // Outer rings
    final outerRadius = radius * 0.57;

    // Blue ring
    final blueRingRect = Rect.fromCircle(center: center, radius: outerRadius);
    final blueRingPaint = Paint()
      ..shader = blueGradient.createShader(blueRingRect)
      ..style = PaintingStyle.stroke
      ..strokeWidth = size.width * 0.06;
    canvas.drawCircle(center, outerRadius, blueRingPaint);

    // Red ring (partial)
    final redRingPaint = Paint()
      ..shader = redGradient.createShader(blueRingRect)
      ..style = PaintingStyle.stroke
      ..strokeWidth = size.width * 0.06
      ..strokeCap = StrokeCap.round;

    canvas.drawArc(
      Rect.fromCircle(center: center, radius: outerRadius),
      math.pi / 4, // Start angle (45 degrees)
      math.pi, // Sweep angle (180 degrees)
      false,
      redRingPaint,
    );

    // Inner clock face
    final innerClockPaint = Paint()
      ..color = const Color(0xFF34495E)
      ..style = PaintingStyle.fill;
    canvas.drawCircle(center, radius * 0.4, innerClockPaint);

    // Clock hands
    final handPaint = Paint()
      ..shader = redGradient.createShader(blueRingRect)
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round;

    // Hour hand (pointing to 10)
    handPaint.strokeWidth = size.width * 0.025;
    final hourHandEnd = Offset(
      center.dx - radius * 0.2,
      center.dy - radius * 0.25,
    );
    canvas.drawLine(center, hourHandEnd, handPaint);

    // Minute hand (pointing to 2)
    handPaint.strokeWidth = size.width * 0.02;
    final minuteHandEnd = Offset(
      center.dx + radius * 0.25,
      center.dy - radius * 0.2,
    );
    canvas.drawLine(center, minuteHandEnd, handPaint);

    // Center dot
    final centerDotPaint = Paint()
      ..shader = redGradient.createShader(blueRingRect)
      ..style = PaintingStyle.fill;
    canvas.drawCircle(center, radius * 0.03, centerDotPaint);

    // Clock markers
    final markerPaint = Paint()
      ..shader = blueGradient.createShader(blueRingRect)
      ..style = PaintingStyle.fill;

    final markerRadius = radius * 0.015;

    // 12 o'clock
    canvas.drawCircle(
      Offset(center.dx, center.dy - radius * 0.32),
      markerRadius,
      markerPaint,
    );

    // 3 o'clock
    canvas.drawCircle(
      Offset(center.dx + radius * 0.32, center.dy),
      markerRadius,
      markerPaint,
    );

    // 6 o'clock
    canvas.drawCircle(
      Offset(center.dx, center.dy + radius * 0.32),
      markerRadius,
      markerPaint,
    );

    // 9 o'clock
    canvas.drawCircle(
      Offset(center.dx - radius * 0.32, center.dy),
      markerRadius,
      markerPaint,
    );

    // Small accent circle (top right)
    final accentPaint = Paint()
      ..shader = redGradient.createShader(blueRingRect)
      ..style = PaintingStyle.fill;
    canvas.drawCircle(
      Offset(center.dx + radius * 0.6, center.dy - radius * 0.6),
      radius * 0.1,
      accentPaint,
    );

    // Corner accents for full coverage
    final cornerAccentPaint = Paint()..style = PaintingStyle.fill;

    // Top-left corner accent
    cornerAccentPaint.shader = blueGradient.createShader(blueRect);
    cornerAccentPaint.color = cornerAccentPaint.color.withValues(alpha: 0.3);
    final topLeftPath = Path()
      ..moveTo(0, size.height * 0.2)
      ..quadraticBezierTo(0, 0, size.width * 0.2, 0)
      ..lineTo(0, 0)
      ..close();
    canvas.drawPath(topLeftPath, cornerAccentPaint);

    // Bottom-right corner accent
    cornerAccentPaint.shader = redGradient.createShader(redRect);
    final bottomRightPath = Path()
      ..moveTo(size.width * 0.8, size.height)
      ..quadraticBezierTo(
        size.width,
        size.height,
        size.width,
        size.height * 0.8,
      )
      ..lineTo(size.width, size.height)
      ..close();
    canvas.drawPath(bottomRightPath, cornerAccentPaint);

    // Additional corner dots
    final dotPaint = Paint()..style = PaintingStyle.fill;

    // Top-right corner dot
    dotPaint.shader = blueGradient.createShader(blueRect);
    dotPaint.color = dotPaint.color.withValues(alpha: 0.6);
    canvas.drawCircle(
      Offset(size.width * 0.8, size.height * 0.2),
      radius * 0.05,
      dotPaint,
    );

    // Bottom-left corner dot
    dotPaint.shader = redGradient.createShader(redRect);
    canvas.drawCircle(
      Offset(size.width * 0.2, size.height * 0.8),
      radius * 0.05,
      dotPaint,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
