import 'dart:io';
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:share_plus/share_plus.dart';
import 'package:url_launcher/url_launcher.dart';
import '../models/file_info.dart';
import 'dart:typed_data'; // Add this import for Uint8List

class AndroidDownloadService {
  static Future<String?> saveCsvFile(String fileName, String content) async {
    debugPrint('AndroidDownloadService: Saving CSV file locally');
    debugPrint('AndroidDownloadService: Filename: $fileName');

    try {
      // Use app-specific storage for Android 10+ (no permissions needed)
      Directory directory = await getApplicationDocumentsDirectory();

      // For Android 9 and below, check storage permissions
      if (Platform.isAndroid) {
        var status = await Permission.storage.status;
        if (!status.isGranted) {
          status = await Permission.storage.request();
          if (!status.isGranted) {
            debugPrint('AndroidDownloadService: Storage permission denied');
            throw Exception('Impossible de sauvegarder le fichier CSV: Permission refusée');
          }
        }
      }

      // Construct the full file path
      final file = File('${directory.path}/$fileName');
      await file.writeAsString(content);

      final fileSize = await file.length();
      debugPrint('AndroidDownloadService: File saved at ${file.path} (size: $fileSize bytes)');
      return file.path;
    } catch (e) {
      debugPrint('AndroidDownloadService: Error saving file: $e');
      throw Exception('Impossible de sauvegarder le fichier CSV: $e');
    }
  }

  static Future<List<FileInfo>> getDownloadedReports() async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      if (!await directory.exists()) {
        debugPrint('AndroidDownloadService: Documents directory not available');
        return [];
      }

      final files = directory.listSync().whereType<File>().toList();
      final reportFiles = files
          .where((file) => file.path.endsWith('.xlsx') || file.path.endsWith('.csv'))
          .map((file) async {
            final stat = await file.stat();
            return FileInfo(
              path: file.path,
              name: file.path.split('/').last,
              size: stat.size,
              modifiedDate: stat.modified,
            );
          })
          .toList();
      final reports = await Future.wait(reportFiles);
      debugPrint('AndroidDownloadService: Found ${reports.length} downloaded reports');
      return reports;
    } catch (e) {
      debugPrint('AndroidDownloadService: Error getting downloaded reports: $e');
      return [];
    }
  }

  static Future<String?> saveAndOpenExcelFile(String fileName, Uint8List bytes) async {
    try {
      // Save the file
      Directory directory = await getApplicationDocumentsDirectory();
      final file = File('${directory.path}/$fileName');
      await file.writeAsBytes(bytes);

      final fileSize = await file.length();
      debugPrint('AndroidDownloadService: File saved at ${file.path} (size: $fileSize bytes)');

      // Open the file
      final success = await openExcelFile(file.path);
      if (success) {
        return file.path;
      } else {
        throw Exception('Impossible d\'ouvrir le fichier après sauvegarde');
      }
    } catch (e) {
      debugPrint('AndroidDownloadService: Error saving and opening Excel file: $e');
      throw Exception('Impossible de sauvegarder et ouvrir le fichier: $e');
    }
  }

  static Future<bool> openExcelFile(String filePath) async {
    try {
      final uri = Uri.file(filePath);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
        debugPrint('AndroidDownloadService: File opened successfully: $filePath');
        return true;
      } else {
        debugPrint('AndroidDownloadService: Could not launch $filePath');
        throw Exception('Impossible d\'ouvrir le fichier: $filePath');
      }
    } catch (e) {
      debugPrint('AndroidDownloadService: Error opening file: $e');
      throw Exception('Impossible d\'ouvrir le fichier: $e');
    }
  }

  static Future<bool> shareExcelFile(String filePath, String fileName) async {
    try {
      await Share.shareXFiles([XFile(filePath)], text: 'Voici votre rapport: $fileName');
      debugPrint('AndroidDownloadService: File shared successfully: $filePath');
      return true;
    } catch (e) {
      debugPrint('AndroidDownloadService: Error sharing file: $e');
      throw Exception('Impossible de partager le fichier: $e');
    }
  }

  static Future<bool> shareViaWhatsApp(String filePath, String fileName) async {
    try {
      await Share.shareXFiles(
        [XFile(filePath)],
        text: 'Rapport: $fileName',
        subject: 'Rapport',
      );
      debugPrint('AndroidDownloadService: File shared via WhatsApp: $filePath');
      return true;
    } catch (e) {
      debugPrint('AndroidDownloadService: Error sharing via WhatsApp: $e');
      throw Exception('Impossible de partager via WhatsApp: $e');
    }
  }

  static Future<bool> shareViaEmail(String filePath, String fileName) async {
    try {
      await Share.shareXFiles(
        [XFile(filePath)],
        text: 'Veuillez trouver le rapport attaché.',
        subject: 'Rapport: $fileName',
      );
      debugPrint('AndroidDownloadService: File shared via email: $filePath');
      return true;
    } catch (e) {
      debugPrint('AndroidDownloadService: Error sharing via email: $e');
      throw Exception('Impossible de partager via email: $e');
    }
  }

  static Future<bool> deleteReport(String filePath) async {
    try {
      final file = File(filePath);
      if (await file.exists()) {
        await file.delete();
        debugPrint('AndroidDownloadService: File deleted at $filePath');
        return true;
      } else {
        debugPrint('AndroidDownloadService: File does not exist at $filePath');
        return false;
      }
    } catch (e) {
      debugPrint('AndroidDownloadService: Error deleting file: $e');
      throw Exception('Impossible de supprimer le fichier: $e');
    }
  }

  static String formatFileSize(int bytes) {
    if (bytes >= 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(2)} MB';
    } else if (bytes >= 1024) {
      return '${(bytes / 1024).toStringAsFixed(2)} KB';
    } else {
      return '$bytes bytes';
    }
  }
}