import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_constants.dart';
import '../../providers/auth_provider.dart';

class ChangePasswordScreen extends StatefulWidget {
  const ChangePasswordScreen({super.key});

  @override
  State<ChangePasswordScreen> createState() => _ChangePasswordScreenState();
}

class _ChangePasswordScreenState extends State<ChangePasswordScreen> {
  final _formKey = GlobalKey<FormState>();
  final _currentPasswordController = TextEditingController();
  final _newPasswordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  
  bool _isCurrentPasswordVisible = false;
  bool _isNewPasswordVisible = false;
  bool _isConfirmPasswordVisible = false;
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('تغيير كلمة المرور'),
        backgroundColor: AppColors.background,
        foregroundColor: AppColors.textPrimary,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildInstructions(),
              const SizedBox(height: AppConstants.largePadding),
              _buildPasswordFields(),
              const SizedBox(height: AppConstants.largePadding),
              _buildPasswordRequirements(),
              const SizedBox(height: AppConstants.largePadding),
              _buildActionButtons(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInstructions() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      decoration: BoxDecoration(
        color: AppColors.background.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        border: Border.all(color: AppColors.background.withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.info, color: AppColors.buttonText),
              const SizedBox(width: 8),
              Text(
                'تعليمات مهمة',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: AppColors.buttonText,
                ),
              ),
            ],
          ),
          const SizedBox(height: AppConstants.smallPadding),
          const Text(
            '• تأكد من إدخال كلمة المرور الحالية بشكل صحيح\n'
            '• اختر كلمة مرور قوية تحتوي على أحرف وأرقام ورموز\n'
            '• لا تشارك كلمة المرور مع أي شخص آخر\n'
            '• ستحتاج لتسجيل الدخول مرة أخرى بعد تغيير كلمة المرور',
            style: TextStyle(
              color: AppColors.buttonText,
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPasswordFields() {
    return Card(
      color: AppColors.background,
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'تغيير كلمة المرور',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppColors.buttonText,
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            TextFormField(
              controller: _currentPasswordController,
              obscureText: !_isCurrentPasswordVisible,
              decoration: InputDecoration(
                labelText: 'كلمة المرور الحالية *',
                prefixIcon: const Icon(Icons.lock_outline, color: AppColors.textPrimary),
                suffixIcon: IconButton(
                  icon: Icon(
                    _isCurrentPasswordVisible ? Icons.visibility : Icons.visibility_off,
                    color: AppColors.textPrimary,
                  ),
                  onPressed: () {
                    setState(() {
                      _isCurrentPasswordVisible = !_isCurrentPasswordVisible;
                    });
                  },
                ),
                border: const OutlineInputBorder(),
                labelStyle: TextStyle(color: AppColors.textPrimary),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'كلمة المرور الحالية مطلوبة';
                }
                return null;
              },
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            TextFormField(
              controller: _newPasswordController,
              obscureText: !_isNewPasswordVisible,
              decoration: InputDecoration(
                labelText: 'كلمة المرور الجديدة *',
                prefixIcon: const Icon(Icons.lock, color: AppColors.textPrimary),
                suffixIcon: IconButton(
                  icon: Icon(
                    _isNewPasswordVisible ? Icons.visibility : Icons.visibility_off,
                    color: AppColors.textPrimary,
                  ),
                  onPressed: () {
                    setState(() {
                      _isNewPasswordVisible = !_isNewPasswordVisible;
                    });
                  },
                ),
                border: const OutlineInputBorder(),
                labelStyle: TextStyle(color: AppColors.textPrimary),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'كلمة المرور الجديدة مطلوبة';
                }
                if (value.length < 8) {
                  return 'كلمة المرور يجب أن تكون 8 أحرف على الأقل';
                }
                if (!_isPasswordStrong(value)) {
                  return 'كلمة المرور ضعيفة. يجب أن تحتوي على أحرف وأرقام';
                }
                return null;
              },
              onChanged: (value) {
                setState(() {}); // Refresh to update password strength indicator
              },
            ),
            const SizedBox(height: AppConstants.smallPadding),
            _buildPasswordStrengthIndicator(_newPasswordController.text),
            const SizedBox(height: AppConstants.defaultPadding),
            TextFormField(
              controller: _confirmPasswordController,
              obscureText: !_isConfirmPasswordVisible,
              decoration: InputDecoration(
                labelText: 'تأكيد كلمة المرور الجديدة *',
                prefixIcon: const Icon(Icons.lock_reset, color: AppColors.textPrimary),
                suffixIcon: IconButton(
                  icon: Icon(
                    _isConfirmPasswordVisible ? Icons.visibility : Icons.visibility_off,
                    color: AppColors.textPrimary,
                  ),
                  onPressed: () {
                    setState(() {
                      _isConfirmPasswordVisible = !_isConfirmPasswordVisible;
                    });
                  },
                ),
                border: const OutlineInputBorder(),
                labelStyle: TextStyle(color: AppColors.textPrimary),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'تأكيد كلمة المرور مطلوب';
                }
                if (value != _newPasswordController.text) {
                  return 'كلمة المرور غير متطابقة';
                }
                return null;
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPasswordStrengthIndicator(String password) {
    final strength = _getPasswordStrength(password);
    final strengthText = _getPasswordStrengthText(strength);
    final strengthColor = _getPasswordStrengthColor(strength);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              'قوة كلمة المرور: ',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(color: AppColors.textPrimary),
            ),
            Text(
              strengthText,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: strengthColor,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        const SizedBox(height: 4),
        LinearProgressIndicator(
          value: strength / 4,
          backgroundColor: AppColors.background.withOpacity(0.3),
          valueColor: AlwaysStoppedAnimation<Color>(strengthColor),
        ),
      ],
    );
  }

  Widget _buildPasswordRequirements() {
    return Card(
      color: AppColors.background,
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'متطلبات كلمة المرور',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppColors.buttonText,
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            _buildRequirementItem(
              'على الأقل 8 أحرف',
              _newPasswordController.text.length >= 8,
            ),
            _buildRequirementItem(
              'يحتوي على أحرف كبيرة وصغيرة',
              _hasUpperAndLowerCase(_newPasswordController.text),
            ),
            _buildRequirementItem(
              'يحتوي على أرقام',
              _hasNumbers(_newPasswordController.text),
            ),
            _buildRequirementItem(
              'يحتوي على رموز خاصة',
              _hasSpecialCharacters(_newPasswordController.text),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRequirementItem(String requirement, bool isMet) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Icon(
            isMet ? Icons.check_circle : Icons.radio_button_unchecked,
            color: isMet ? AppColors.buttonText : AppColors.textPrimary.withOpacity(0.5),
            size: 20,
          ),
          const SizedBox(width: 8),
          Text(
            requirement,
            style: TextStyle(
              color: isMet ? AppColors.buttonText : AppColors.textPrimary.withOpacity(0.5),
              fontWeight: isMet ? FontWeight.w600 : FontWeight.normal,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton(
            onPressed: _isLoading ? null : () => Navigator.pop(context),
            style: OutlinedButton.styleFrom(
              side: BorderSide(color: AppColors.buttonText),
              foregroundColor: AppColors.buttonText,
            ),
            child: const Text('إلغاء'),
          ),
        ),
        const SizedBox(width: AppConstants.defaultPadding),
        Expanded(
          child: ElevatedButton(
            onPressed: _isLoading ? null : _changePassword,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.buttonBackground,
              foregroundColor: AppColors.buttonText,
            ),
            child: _isLoading
                ? const SizedBox(
                    height: 20,
                    width: 20,
                    child: CircularProgressIndicator(strokeWidth: 2, color: AppColors.buttonText),
                  )
                : const Text('تغيير كلمة المرور'),
          ),
        ),
      ],
    );
  }

  int _getPasswordStrength(String password) {
    int strength = 0;
    if (password.length >= 8) strength++;
    if (_hasUpperAndLowerCase(password)) strength++;
    if (_hasNumbers(password)) strength++;
    if (_hasSpecialCharacters(password)) strength++;
    return strength;
  }

  String _getPasswordStrengthText(int strength) {
    switch (strength) {
      case 0:
      case 1:
        return 'ضعيفة جداً';
      case 2:
        return 'ضعيفة';
      case 3:
        return 'متوسطة';
      case 4:
        return 'قوية';
      default:
        return 'ضعيفة جداً';
    }
  }

  Color _getPasswordStrengthColor(int strength) {
    switch (strength) {
      case 0:
      case 1:
        return AppColors.buttonText;
      case 2:
        return AppColors.buttonText.withOpacity(0.7);
      case 3:
        return AppColors.buttonText.withOpacity(0.5);
      case 4:
        return AppColors.buttonText.withOpacity(0.3);
      default:
        return AppColors.buttonText;
    }
  }

  bool _isPasswordStrong(String password) {
    return _getPasswordStrength(password) >= 3;
  }

  bool _hasUpperAndLowerCase(String password) {
    return password.contains(RegExp(r'[A-Z]')) && password.contains(RegExp(r'[a-z]'));
  }

  bool _hasNumbers(String password) {
    return password.contains(RegExp(r'[0-9]'));
  }

  bool _hasSpecialCharacters(String password) {
    return password.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]'));
  }

  void _changePassword() async {
    if (_formKey.currentState?.validate() ?? false) {
      setState(() {
        _isLoading = true;
      });

      try {
        await context.read<AuthProvider>().changePassword(
          currentPassword: _currentPasswordController.text,
          newPassword: _newPasswordController.text,
          confirmPassword: _confirmPasswordController.text,
        );

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم تغيير كلمة المرور بنجاح'),
              backgroundColor: AppColors.buttonText,
            ),
          );
          Navigator.pop(context);
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('فشل في تغيير كلمة المرور: $e'),
              backgroundColor: AppColors.buttonText.withOpacity(0.7),
            ),
          );
        }
      } finally {
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }
      }
    }
  }

  @override
  void dispose() {
    _currentPasswordController.dispose();
    _newPasswordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }
}