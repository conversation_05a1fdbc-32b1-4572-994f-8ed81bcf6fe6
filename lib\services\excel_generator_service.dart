import 'dart:convert';
import 'package:flutter/foundation.dart';

/// Service pour générer de vrais fichiers Excel
class ExcelGeneratorService {
  /// Générer un fichier Excel avec des données de rapport
  static Future<Uint8List> generateExcelReport({
    required String reportType,
    required DateTime startDate,
    required DateTime endDate,
    List<Map<String, dynamic>>? employeeData,
    int? selectedEmployeeId,
    int? selectedSiteId,
  }) async {
    try {
      debugPrint('ExcelGeneratorService: Generating Excel report');
      debugPrint('ExcelGeneratorService: Report type: $reportType');
      debugPrint(
        'ExcelGeneratorService: Date range: ${startDate.toIso8601String()} to ${endDate.toIso8601String()}',
      );

      // Créer un fichier CSV compatible Excel avec BOM UTF-8
      final csvContent = _createExcelCompatibleCSV(
        reportType: reportType,
        startDate: startDate,
        endDate: endDate,
        employeeData: employeeData ?? _getDefaultEmployeeData(),
      );

      // Encoder correctement en UTF-8 avec BOM pour Excel
      final bom = [0xEF, 0xBB, 0xBF];
      final contentBytes = utf8.encode(csvContent);
      final bytes = Uint8List.fromList([...bom, ...contentBytes]);

      debugPrint(
        'ExcelGeneratorService: Excel-compatible CSV generated successfully (${bytes.length} bytes)',
      );
      return bytes;
    } catch (e) {
      debugPrint('ExcelGeneratorService: Error generating Excel: $e');
      rethrow;
    }
  }

  /// Créer un fichier CSV compatible Excel avec support UTF-8
  static String _createExcelCompatibleCSV({
    required String reportType,
    required DateTime startDate,
    required DateTime endDate,
    required List<Map<String, dynamic>> employeeData,
  }) {
    final reportTitle = _getReportTitle(reportType);
    final dateRange = '${_formatDate(startDate)} إلى ${_formatDate(endDate)}';

    // En-tête du rapport
    final header =
        '''$reportTitle
$dateRange

الرقم,اسم الموظف,الموقع,التاريخ,وقت الدخول,وقت الخروج,إجمالي الساعات''';

    // صفوف البيانات
    final dataRows = employeeData
        .map((employee) {
          return '${employee['id'] ?? ''},"${employee['name'] ?? ''}","${employee['site'] ?? ''}","${employee['date'] ?? ''}","${employee['checkIn'] ?? ''}","${employee['checkOut'] ?? ''}","${employee['totalHours'] ?? ''}"';
        })
        .join('\n');

    // إحصائيات
    final stats =
        '''

الإحصائيات:
إجمالي الموظفين:,${employeeData.length}
إجمالي أيام العمل:,${endDate.difference(startDate).inDays + 1}

تم إنشاء هذا التقرير بواسطة تطبيق ClockIn
تاريخ الإنشاء: ${_formatDate(DateTime.now())} ${DateTime.now().hour.toString().padLeft(2, '0')}:${DateTime.now().minute.toString().padLeft(2, '0')}''';

    return '$header\n$dataRows$stats';
  }

  /// الحصول على عنوان التقرير
  static String _getReportTitle(String reportType) {
    switch (reportType) {
      case 'all_employees':
        return 'تقرير حضور جميع الموظفين';
      case 'individual':
        return 'تقرير حضور موظف محدد';
      case 'site':
        return 'تقرير حضور موقع محدد';
      default:
        return 'تقرير الحضور';
    }
  }

  /// تنسيق التاريخ
  static String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }

  /// بيانات الموظفين الافتراضية للاختبار
  static List<Map<String, dynamic>> _getDefaultEmployeeData() {
    final now = DateTime.now();
    return [
      {
        'id': 1,
        'name': 'أحمد محمد علي',
        'site': 'مكتب الرياض الرئيسي',
        'date': _formatDate(now.subtract(Duration(days: 2))),
        'checkIn': '08:00',
        'checkOut': '17:00',
        'totalHours': '9 ساعات',
      },
      {
        'id': 2,
        'name': 'فاطمة سالم أحمد',
        'site': 'مكتب جدة',
        'date': _formatDate(now.subtract(Duration(days: 2))),
        'checkIn': '09:00',
        'checkOut': '18:00',
        'totalHours': '9 ساعات',
      },
      {
        'id': 3,
        'name': 'محمد عبدالله سعد',
        'site': 'مكتب الدمام',
        'date': _formatDate(now.subtract(Duration(days: 2))),
        'checkIn': '08:30',
        'checkOut': '17:30',
        'totalHours': '9 ساعات',
      },
      {
        'id': 4,
        'name': 'نورا خالد محمود',
        'site': 'مكتب الرياض الرئيسي',
        'date': _formatDate(now.subtract(Duration(days: 1))),
        'checkIn': '08:15',
        'checkOut': '17:15',
        'totalHours': '9 ساعات',
      },
      {
        'id': 5,
        'name': 'خالد أحمد عبدالرحمن',
        'site': 'مكتب جدة',
        'date': _formatDate(now.subtract(Duration(days: 1))),
        'checkIn': '09:30',
        'checkOut': '18:30',
        'totalHours': '9 ساعات',
      },
      {
        'id': 6,
        'name': 'سارة محمد الأحمد',
        'site': 'مكتب الدمام',
        'date': _formatDate(now),
        'checkIn': '08:00',
        'checkOut': '17:00',
        'totalHours': '9 ساعات',
      },
      {
        'id': 7,
        'name': 'عبدالرحمن سعد علي',
        'site': 'مكتب الرياض الرئيسي',
        'date': _formatDate(now),
        'checkIn': '08:45',
        'checkOut': '17:45',
        'totalHours': '9 ساعات',
      },
      {
        'id': 8,
        'name': 'مريم عبدالله خالد',
        'site': 'مكتب جدة',
        'date': _formatDate(now),
        'checkIn': '09:15',
        'checkOut': '18:15',
        'totalHours': '9 ساعات',
      },
    ];
  }

  /// إنشاء اسم ملف فريد
  static String generateFileName(String reportType) {
    final now = DateTime.now();
    final timestamp =
        '${now.year}-${now.month.toString().padLeft(2, '0')}-${now.day.toString().padLeft(2, '0')}_${now.hour.toString().padLeft(2, '0')}-${now.minute.toString().padLeft(2, '0')}';
    return 'rapport_${reportType}_$timestamp.csv';
  }
}
